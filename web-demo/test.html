<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>an-Q 登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🎱 Shuan-Q 登录功能测试</h1>
    
    <div class="form-group">
        <label for="phone">手机号:</label>
        <input type="tel" id="phone" value="13800138000" readonly>
    </div>
    
    <div class="form-group">
        <label for="code">验证码:</label>
        <input type="text" id="code" placeholder="请输入验证码">
        <small>测试环境验证码: 123456</small>
    </div>
    
    <button onclick="sendCode()" id="sendBtn">获取验证码</button>
    <button onclick="login()" id="loginBtn">登录</button>
    <button onclick="clearLog()">清空日志</button>
    
    <div id="result"></div>
    <div id="log" class="log"></div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        let authToken = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('result').innerHTML = '';
        }
        
        async function sendCode() {
            const phone = document.getElementById('phone').value;
            const btn = document.getElementById('sendBtn');
            
            btn.disabled = true;
            log('开始发送验证码...');
            
            try {
                log(`请求URL: ${API_BASE}/auth/send-code`);
                log(`请求数据: ${JSON.stringify({ phone })}`);
                
                const response = await fetch(`${API_BASE}/auth/send-code`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    mode: 'cors',
                    body: JSON.stringify({ phone })
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    showResult('✅ 验证码发送成功！', 'success');
                    document.getElementById('code').value = '123456';
                } else {
                    showResult(`❌ 发送失败: ${data.error}`, 'error');
                }
            } catch (error) {
                log(`错误: ${error.message}`);
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
            }
        }
        
        async function login() {
            const phone = document.getElementById('phone').value;
            const code = document.getElementById('code').value;
            const btn = document.getElementById('loginBtn');
            
            if (!code) {
                showResult('请输入验证码', 'error');
                return;
            }
            
            btn.disabled = true;
            log('开始登录...');
            
            try {
                log(`请求URL: ${API_BASE}/auth/login`);
                log(`请求数据: ${JSON.stringify({ phone, code })}`);
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    mode: 'cors',
                    body: JSON.stringify({ phone, code })
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    authToken = data.data.token;
                    showResult(`🎉 登录成功！\n用户: ${data.data.user.nickname}\n手机: ${data.data.user.phone}\nToken: ${authToken.substring(0, 20)}...`, 'success');
                } else {
                    showResult(`❌ 登录失败: ${data.error}`, 'error');
                }
            } catch (error) {
                log(`错误: ${error.message}`);
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
            }
        }
        
        // 页面加载时检查服务器状态
        window.onload = async function() {
            log('页面加载完成，检查服务器状态...');
            
            try {
                const response = await fetch('http://localhost:3000/');
                if (response.ok) {
                    const data = await response.json();
                    log('✅ 后端服务器连接正常');
                    log(`服务器信息: ${data.message} v${data.version}`);
                } else {
                    log('❌ 后端服务器响应异常');
                }
            } catch (error) {
                log('❌ 无法连接到后端服务器');
                log(`错误: ${error.message}`);
                showResult('⚠️ 后端服务器未启动，请先运行: cd backend && NODE_ENV=test node test-server.js', 'error');
            }
        };
    </script>
</body>
</html>
