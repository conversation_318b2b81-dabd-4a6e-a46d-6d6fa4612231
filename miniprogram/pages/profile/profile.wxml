<!--pages/profile/profile.wxml-->
<view class="page-container">
  <view wx:if="{{!isLogin}}" class="login-section">
    <view class="login-avatar">
      <text class="avatar-icon">👤</text>
    </view>
    <text class="login-text">登录 Shuan-Q</text>
    <text class="login-desc">体验专业的台球教学服务</text>
    <button class="btn btn-primary" bindtap="goToLogin">立即登录</button>
  </view>

  <view wx:else class="profile-section">
    <view class="user-info">
      <view class="user-avatar">
        <text class="avatar-text">{{userInfo.nickname.charAt(0)}}</text>
      </view>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickname}}</text>
        <text class="user-phone">{{userInfo.phone}}</text>
        <view class="user-type">{{userInfo.userType === 'student' ? '学员' : '教练'}}</view>
      </view>
    </view>

    <view class="menu-section">
      <view class="menu-item" bindtap="goToOrders">
        <text class="menu-icon">🛒</text>
        <text class="menu-text">我的订单</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item">
        <text class="menu-icon">📅</text>
        <text class="menu-text">我的预约</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item">
        <text class="menu-icon">❤️</text>
        <text class="menu-text">我的收藏</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item">
        <text class="menu-icon">⚙️</text>
        <text class="menu-text">设置</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <button class="btn btn-outline logout-btn" bindtap="logout">退出登录</button>
  </view>
</view>
