{"name": "shuan-q-mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node test-app.js", "test": "node test-api-integration.js", "lint": "echo '<PERSON><PERSON> skipped for demo'"}, "dependencies": {"react": "^18.2.0", "react-native": "^0.72.0", "@reduxjs/toolkit": "^1.9.0", "react-redux": "^8.1.0"}, "devDependencies": {"@types/react": "^18.0.24", "typescript": "4.8.4"}, "keywords": ["react-native", "billiards", "coach", "appointment"], "author": "Your Name", "license": "MIT", "description": "Shuan-Q 台球教练预约移动应用"}