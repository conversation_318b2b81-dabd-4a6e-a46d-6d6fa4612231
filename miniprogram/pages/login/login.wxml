<!--pages/login/login.wxml-->
<view class="page-container">
  <view class="login-container">
    <!-- 登录头部 -->
    <view class="login-header">
      <view class="logo">🎱</view>
      <text class="app-name">Shuan-Q</text>
      <text class="app-desc">专业台球教练预约平台</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <view class="form-group">
        <text class="form-label">手机号</text>
        <input 
          class="form-input" 
          type="number" 
          placeholder="请输入手机号" 
          value="{{phone}}"
          bindinput="onPhoneInput"
          maxlength="11"
        />
      </view>

      <view class="form-group">
        <text class="form-label">验证码</text>
        <view class="input-group">
          <input 
            class="form-input" 
            type="number" 
            placeholder="请输入验证码" 
            value="{{code}}"
            bindinput="onCodeInput"
            maxlength="6"
          />
          <button 
            class="btn btn-outline code-btn" 
            bindtap="sendCode"
            disabled="{{codeLoading || countdown > 0}}"
          >
            {{codeLoading ? '发送中...' : countdown > 0 ? countdown + 's' : '获取验证码'}}
          </button>
        </view>
        <text class="form-hint">测试环境验证码: 123456</text>
      </view>

      <button 
        class="btn btn-primary btn-block login-btn" 
        bindtap="login"
        disabled="{{loginLoading || !phone || !code}}"
      >
        {{loginLoading ? '登录中...' : '登录'}}
      </button>
    </view>

    <!-- 登录说明 -->
    <view class="login-tips">
      <text class="tips-text">登录即表示同意</text>
      <text class="tips-link">《用户协议》</text>
      <text class="tips-text">和</text>
      <text class="tips-link">《隐私政策》</text>
    </view>
  </view>
</view>
