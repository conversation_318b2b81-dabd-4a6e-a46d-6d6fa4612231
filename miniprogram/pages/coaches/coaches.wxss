/* pages/coaches/coaches.wxss */

/* 搜索栏 */
.search-bar {
  padding: 20rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-input-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 0 48rpx 0 32rpx;
}

.search-input {
  height: 72rpx;
  line-height: 72rpx;
  font-size: 28rpx;
  width: 100%;
}

.search-icon {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #999;
}

/* 筛选栏 */
.filter-bar {
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 20rpx 0;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-list {
  display: flex;
  padding: 0 20rpx;
  gap: 24rpx;
}

.filter-item {
  padding: 16rpx 32rpx;
  background: #f8f9fa;
  border-radius: 32rpx;
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s;
}

.filter-item.active {
  background: #5a9178;
  color: #fff;
}

/* 教练列表容器 */
.coaches-container {
  flex: 1;
  padding: 20rpx;
}

.coaches-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

/* 教练卡片 */
.coach-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  display: flex;
  gap: 24rpx;
  transition: all 0.3s;
}

.coach-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 教练头像 */
.coach-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #5a9178, #4a7c59);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-text {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
}

/* 教练信息 */
.coach-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.coach-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.coach-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.coach-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-stars {
  font-size: 24rpx;
  color: #ffc107;
}

.rating-score {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffc107;
}

.coach-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 24rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 教练详情 */
.coach-details {
  margin-bottom: 24rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 24rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.detail-text {
  color: #666;
  flex: 1;
}

/* 教练底部 */
.coach-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.coach-price {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.price-label {
  font-size: 24rpx;
  color: #999;
}

.price-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #e74c3c;
}

.coach-book-btn {
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  border-radius: 32rpx;
  min-width: 120rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 20rpx;
}

.load-more .btn {
  min-width: 200rpx;
}
