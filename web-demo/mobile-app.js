// Shuan-Q 移动端UI演示
const API_BASE = 'http://localhost:3000/api';
let authToken = null;
let currentUser = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('移动端UI加载完成');
    checkAuthStatus();
});

// 显示标签页
function showTab(tabName) {
    // 隐藏所有标签页内容
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });
    
    // 移除所有标签页按钮的激活状态
    const tabItems = document.querySelectorAll('.tab-item');
    tabItems.forEach(item => {
        item.classList.remove('active');
    });
    
    // 显示选中的标签页
    const selectedTab = document.getElementById(tabName);
    if (selectedTab) {
        selectedTab.classList.add('active', 'fade-in');
    }
    
    // 激活对应的标签页按钮
    const selectedTabItem = document.querySelector(`[onclick="showTab('${tabName}')"]`);
    if (selectedTabItem) {
        selectedTabItem.classList.add('active');
    }
    
    // 如果是订单页面且已登录，加载订单数据
    if (tabName === 'orders' && authToken) {
        loadMobileOrders();
    }
}

// 检查登录状态
function checkAuthStatus() {
    if (authToken && currentUser) {
        updateProfileUI();
        loadMobileOrders();
    }
}

// 发送验证码
async function sendMobileCode() {
    const phone = document.getElementById('mobile-phone').value;
    
    try {
        const response = await fetch(`${API_BASE}/auth/send-code`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ phone })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMobileAlert('✅ 验证码发送成功', 'success');
            document.getElementById('mobile-code').value = '123456';
        } else {
            showMobileAlert(`❌ ${data.error}`, 'error');
        }
    } catch (error) {
        showMobileAlert(`❌ 网络错误: ${error.message}`, 'error');
    }
}

// 移动端登录
async function mobileLogin() {
    const phone = document.getElementById('mobile-phone').value;
    const code = document.getElementById('mobile-code').value;
    const loadingSpinner = document.getElementById('login-loading');
    
    if (!code) {
        showMobileAlert('请输入验证码', 'error');
        return;
    }
    
    // 显示加载状态
    loadingSpinner.style.display = 'inline-block';
    
    try {
        const response = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ phone, code })
        });
        
        const data = await response.json();
        
        if (data.success) {
            authToken = data.data.token;
            currentUser = data.data.user;
            
            showMobileAlert(`🎉 登录成功！欢迎 ${currentUser.nickname}`, 'success');
            updateProfileUI();
            loadMobileOrders();
            
        } else {
            showMobileAlert(`❌ 登录失败: ${data.error}`, 'error');
        }
    } catch (error) {
        showMobileAlert(`❌ 网络错误: ${error.message}`, 'error');
    } finally {
        loadingSpinner.style.display = 'none';
    }
}

// 更新个人中心UI
function updateProfileUI() {
    const profileContent = document.getElementById('profile-content');
    
    if (authToken && currentUser) {
        profileContent.innerHTML = `
            <div style="padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <div style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #5a9178, #4a7c59); display: flex; align-items: center; justify-content: center; margin: 0 auto 16px; color: #fff; font-size: 32px;">
                        <i class="fas fa-user"></i>
                    </div>
                    <h2>${currentUser.nickname}</h2>
                    <p style="color: #666;">${currentUser.phone}</p>
                    <span class="badge">${currentUser.userType === 'student' ? '学员' : '教练'}</span>
                </div>
                
                <div class="card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <span><i class="fas fa-shopping-cart" style="margin-right: 8px; color: #5a9178;"></i>我的订单</span>
                        <span style="color: #5a9178;">></span>
                    </div>
                    <div style="display: flex; justify-content: space-around; text-align: center;">
                        <div onclick="showTab('orders')">
                            <div style="font-size: 20px; color: #666;"><i class="fas fa-clock"></i></div>
                            <div style="font-size: 12px; color: #666; margin-top: 4px;">待支付</div>
                        </div>
                        <div onclick="showTab('orders')">
                            <div style="font-size: 20px; color: #666;"><i class="fas fa-check-circle"></i></div>
                            <div style="font-size: 12px; color: #666; margin-top: 4px;">已支付</div>
                        </div>
                        <div onclick="showTab('orders')">
                            <div style="font-size: 20px; color: #666;"><i class="fas fa-star"></i></div>
                            <div style="font-size: 12px; color: #666; margin-top: 4px;">待评价</div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <span><i class="fas fa-calendar-alt" style="margin-right: 8px; color: #5a9178;"></i>我的预约</span>
                            <span style="color: #5a9178;">></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <span><i class="fas fa-heart" style="margin-right: 8px; color: #5a9178;"></i>我的收藏</span>
                            <span style="color: #5a9178;">></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span><i class="fas fa-cog" style="margin-right: 8px; color: #5a9178;"></i>设置</span>
                            <span style="color: #5a9178;">></span>
                        </div>
                    </div>
                </div>
                
                <button class="btn btn-outline" onclick="logout()" style="margin-top: 20px;">
                    退出登录
                </button>
            </div>
        `;
    }
}

// 加载移动端订单
async function loadMobileOrders() {
    if (!authToken) return;
    
    const ordersContent = document.getElementById('orders-content');
    
    // 显示加载状态
    ordersContent.innerHTML = `
        <div class="loading">
            <div class="spinner"></div>
            <p>加载订单中...</p>
        </div>
    `;
    
    try {
        const response = await fetch(`${API_BASE}/orders/my`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            if (data.data.orders.length === 0) {
                ordersContent.innerHTML = `
                    <div style="text-align: center; padding: 40px 20px; color: #666;">
                        <i class="fas fa-shopping-cart" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                        <p>暂无订单</p>
                        <button class="btn btn-primary" style="margin-top: 16px;" onclick="showTab('packages')">
                            去购买课程包
                        </button>
                    </div>
                `;
            } else {
                let ordersHTML = '';
                data.data.orders.forEach(order => {
                    ordersHTML += createMobileOrderCard(order);
                });
                ordersContent.innerHTML = ordersHTML;
            }
        } else {
            ordersContent.innerHTML = `
                <div style="text-align: center; padding: 40px 20px; color: #666;">
                    <p>加载订单失败</p>
                    <button class="btn btn-primary" onclick="loadMobileOrders()">重试</button>
                </div>
            `;
        }
    } catch (error) {
        ordersContent.innerHTML = `
            <div style="text-align: center; padding: 40px 20px; color: #666;">
                <p>网络错误</p>
                <button class="btn btn-primary" onclick="loadMobileOrders()">重试</button>
            </div>
        `;
    }
}

// 创建移动端订单卡片
function createMobileOrderCard(order) {
    const statusMap = {
        'pending': { text: '待支付', class: 'status-pending' },
        'paid': { text: '已支付', class: 'status-paid' },
        'completed': { text: '已完成', class: 'status-completed' }
    };
    
    const status = statusMap[order.status] || { text: order.status, class: 'status-pending' };
    
    return `
        <div class="card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                <h4 style="font-size: 16px; margin: 0;">${order.package?.name || '未知课程包'}</h4>
                <span class="order-status ${status.class}">${status.text}</span>
            </div>
            <div style="color: #666; font-size: 14px; margin-bottom: 8px;">
                订单号: ${order.orderNumber}
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <div style="color: #e74c3c; font-size: 18px; font-weight: bold;">¥${order.amount}</div>
                    <div style="color: #666; font-size: 12px;">
                        ${new Date(order.createdAt).toLocaleDateString()}
                    </div>
                </div>
                <div>
                    ${order.status === 'pending' ? 
                        `<button class="btn btn-primary" style="padding: 8px 16px; font-size: 12px;" onclick="payMobileOrder('${order.id}')">立即支付</button>` :
                        `<button class="btn btn-outline" style="padding: 8px 16px; font-size: 12px;">查看详情</button>`
                    }
                </div>
            </div>
        </div>
    `;
}

// 购买课程包
async function purchasePackage(packageId) {
    if (!authToken) {
        showMobileAlert('请先登录', 'error');
        showTab('profile');
        return;
    }
    
    try {
        // 创建订单
        const orderResponse = await fetch(`${API_BASE}/orders`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                packageId,
                paymentMethod: 'wechat'
            })
        });
        
        const orderData = await orderResponse.json();
        
        if (orderData.success) {
            showMobileAlert('✅ 订单创建成功！', 'success');
            
            // 自动跳转到订单页面
            setTimeout(() => {
                showTab('orders');
            }, 1500);
            
        } else {
            showMobileAlert(`❌ 订单创建失败: ${orderData.error}`, 'error');
        }
    } catch (error) {
        showMobileAlert(`❌ 购买失败: ${error.message}`, 'error');
    }
}

// 支付订单
async function payMobileOrder(orderId) {
    try {
        const response = await fetch(`${API_BASE}/orders/${orderId}/pay`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMobileAlert('🎉 支付成功！', 'success');
            loadMobileOrders(); // 刷新订单列表
        } else {
            showMobileAlert(`❌ 支付失败: ${data.error}`, 'error');
        }
    } catch (error) {
        showMobileAlert(`❌ 支付失败: ${error.message}`, 'error');
    }
}

// 显示移动端提示
function showMobileAlert(message, type = 'info') {
    // 移除现有的提示
    const existingAlert = document.querySelector('.mobile-alert');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    // 创建新的提示
    const alert = document.createElement('div');
    alert.className = 'mobile-alert';
    alert.style.cssText = `
        position: fixed;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        max-width: 300px;
        text-align: center;
        font-size: 14px;
        animation: slideDown 0.3s ease-out;
    `;
    alert.textContent = message;
    
    document.body.appendChild(alert);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (alert.parentNode) {
            alert.style.animation = 'slideUp 0.3s ease-out';
            setTimeout(() => alert.remove(), 300);
        }
    }, 3000);
}

// 退出登录
function logout() {
    authToken = null;
    currentUser = null;
    
    // 重置个人中心UI
    const profileContent = document.getElementById('profile-content');
    profileContent.innerHTML = `
        <div class="login-form">
            <div style="text-align: center; margin-bottom: 30px;">
                <i class="fas fa-user-circle" style="font-size: 80px; color: #ddd;"></i>
                <h2 style="margin-top: 16px;">登录 Shuan-Q</h2>
                <p style="color: #666;">体验专业的台球教学服务</p>
            </div>
            
            <div class="form-group">
                <label class="form-label">手机号</label>
                <input type="tel" class="form-input" id="mobile-phone" value="13800138000" readonly>
            </div>
            
            <div class="form-group">
                <label class="form-label">验证码</label>
                <div class="input-group">
                    <input type="text" class="form-input" id="mobile-code" placeholder="请输入验证码">
                    <button class="btn btn-outline" onclick="sendMobileCode()">获取验证码</button>
                </div>
                <small style="color: #666; font-size: 12px;">测试环境验证码: 123456</small>
            </div>
            
            <button class="btn btn-primary" onclick="mobileLogin()">
                <span id="login-loading" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i>
                </span>
                登录
            </button>
            
            <div id="mobile-result" style="margin-top: 16px;"></div>
        </div>
    `;
    
    // 重置订单页面
    const ordersContent = document.getElementById('orders-content');
    ordersContent.innerHTML = `
        <div style="text-align: center; padding: 40px 20px; color: #666;">
            <i class="fas fa-shopping-cart" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
            <p>请先登录查看订单</p>
            <button class="btn btn-primary" style="margin-top: 16px;" onclick="showLogin()">
                立即登录
            </button>
        </div>
    `;
    
    showMobileAlert('已退出登录', 'info');
}

// 显示登录页面
function showLogin() {
    showTab('profile');
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { transform: translate(-50%, -100%); opacity: 0; }
        to { transform: translate(-50%, 0); opacity: 1; }
    }
    @keyframes slideUp {
        from { transform: translate(-50%, 0); opacity: 1; }
        to { transform: translate(-50%, -100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
