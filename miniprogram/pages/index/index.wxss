/* pages/index/index.wxss */

/* 英雄区域 */
.hero-section {
  background: linear-gradient(135deg, #5a9178, #4a7c59);
  padding: 60rpx 40rpx;
  margin-bottom: 32rpx;
}

.hero-content {
  text-align: center;
  color: #fff;
}

.hero-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.hero-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 统计数据 */
.stats-section {
  margin: 0 20rpx 32rpx;
}

.stats-grid {
  display: flex;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.stats-item {
  flex: 1;
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #5a9178;
  margin-bottom: 8rpx;
}

.stats-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 快速入口 */
.quick-actions {
  margin: 0 20rpx 32rpx;
}

.actions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 32rpx;
}

.action-item {
  flex: 1;
  min-width: 140rpx;
  text-align: center;
  padding: 32rpx 16rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s;
}

.action-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.action-text {
  display: block;
  font-size: 24rpx;
  color: #333;
}

/* 热门课程包 */
.hot-packages {
  margin: 0 20rpx 32rpx;
}

.more-link {
  color: #5a9178;
  font-size: 28rpx;
}

.packages-scroll {
  white-space: nowrap;
}

.packages-list {
  display: flex;
  gap: 24rpx;
  padding: 16rpx 0;
}

.package-item {
  width: 320rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  flex-shrink: 0;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.package-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}

.package-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.package-info {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.package-sessions,
.package-validity {
  font-size: 20rpx;
  color: #999;
  background: #fff;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}

/* 优秀教练 */
.top-coaches {
  margin: 0 20rpx 32rpx;
}

.coaches-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.coach-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s;
}

.coach-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.coach-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #5a9178, #4a7c59);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.coach-info {
  flex: 1;
}

.coach-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.coach-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.rating-text {
  font-size: 24rpx;
  color: #ffc107;
}

.rating-count {
  font-size: 20rpx;
  color: #999;
}

.coach-location {
  font-size: 24rpx;
  color: #666;
}

.coach-price {
  text-align: right;
}

.price-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #e74c3c;
}
