# 🔍 Shuan-Q 项目开发问题复盘分析

## 1. Node.js 和 npm 环境问题

### 问题表现
- `npm: command not found`
- `npx: command not found` 
- 包安装失败或依赖冲突
- 版本不兼容问题

### 根本原因分析
```bash
# 常见的环境检查命令
node --version    # 检查Node.js版本
npm --version     # 检查npm版本
which node        # 检查Node.js安装路径
echo $PATH        # 检查环境变量配置
```

### 解决方案
```bash
# 1. 安装Node.js (推荐使用LTS版本)
# macOS
brew install node

# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# Windows
# 下载官方安装包: https://nodejs.org/

# 2. 验证安装
node --version    # 应该显示 v18.x.x 或更高
npm --version     # 应该显示 9.x.x 或更高

# 3. 配置npm镜像源(解决网络问题)
npm config set registry https://registry.npmmirror.com/
```

## 2. 端口占用和服务冲突问题

### 问题表现
- `Error: listen EADDRINUSE: address already in use :::3000`
- 服务启动失败
- 多个服务实例冲突

### 解决方案
```bash
# 查找占用端口的进程
lsof -i :3000
netstat -tulpn | grep :3000

# 杀死占用进程
kill -9 <PID>

# 或者使用不同端口
PORT=3001 node server.js
```

## 3. 权限问题

### 问题表现
- `EACCES: permission denied`
- 无法创建文件或目录
- npm全局安装失败

### 解决方案
```bash
# 1. 修复npm权限问题
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 2. 使用sudo (不推荐，但有时必要)
sudo npm install -g <package>

# 3. 修改文件权限
chmod 755 <file>
chown $USER:$USER <directory>
```

## 4. 依赖版本冲突问题

### 问题表现
- `npm ERR! peer dep missing`
- 版本不兼容警告
- 运行时错误

### 解决方案
```bash
# 1. 清理缓存和重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# 2. 使用特定版本
npm install express@4.18.0

# 3. 解决peer依赖
npm install --legacy-peer-deps
```

## 5. 跨平台兼容性问题

### 问题表现
- Windows路径分隔符问题
- 脚本执行权限问题
- 换行符差异

### 解决方案
```javascript
// 使用path模块处理路径
const path = require('path');
const filePath = path.join(__dirname, 'data', 'file.json');

// 使用cross-env处理环境变量
npm install --save-dev cross-env
// package.json
"scripts": {
  "start": "cross-env NODE_ENV=production node server.js"
}
```

## 6. 网络和防火墙问题

### 问题表现
- npm安装超时
- API请求失败
- CORS错误

### 解决方案
```bash
# 1. 配置npm代理
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080

# 2. 使用国内镜像
npm config set registry https://registry.npmmirror.com/

# 3. 配置CORS (服务器端)
app.use(cors({
  origin: ['http://localhost:8080', 'http://127.0.0.1:8080'],
  credentials: true
}));
```

## 7. 开发工具配置问题

### 问题表现
- VSCode插件冲突
- 微信开发者工具无法加载项目
- 浏览器缓存问题

### 解决方案
```bash
# 1. 清理浏览器缓存
# Chrome: Ctrl+Shift+Delete

# 2. 微信开发者工具配置
# - 勾选"不校验合法域名"
# - 清除缓存重新编译

# 3. VSCode配置
# 禁用冲突插件，重启编辑器
```

## 8. 数据库连接问题

### 问题表现
- 数据库连接超时
- 认证失败
- 数据格式错误

### 解决方案
```javascript
// 1. 检查数据库服务状态
// MySQL
sudo systemctl status mysql

// 2. 测试连接
const mysql = require('mysql2');
const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: 'password',
  database: 'shuan_q'
});

connection.connect((err) => {
  if (err) {
    console.error('数据库连接失败:', err);
    return;
  }
  console.log('数据库连接成功');
});
```

## 9. 内存和性能问题

### 问题表现
- `JavaScript heap out of memory`
- 服务响应缓慢
- 进程崩溃

### 解决方案
```bash
# 1. 增加Node.js内存限制
node --max-old-space-size=4096 server.js

# 2. 监控内存使用
npm install -g clinic
clinic doctor -- node server.js

# 3. 优化代码
# - 避免内存泄漏
# - 使用流处理大文件
# - 实现缓存机制
```

## 10. 部署环境问题

### 问题表现
- 生产环境配置错误
- 环境变量缺失
- SSL证书问题

### 解决方案
```bash
# 1. 环境变量管理
# .env文件
NODE_ENV=production
PORT=3000
DB_HOST=localhost
DB_USER=root
DB_PASS=password

# 2. 使用PM2管理进程
npm install -g pm2
pm2 start ecosystem.config.js
pm2 monit

# 3. SSL配置
# 使用Let's Encrypt或购买SSL证书
```

## 🛠️ 预防措施和最佳实践

### 1. 环境标准化
```bash
# 使用nvm管理Node.js版本
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install --lts
nvm use --lts
```

### 2. 依赖管理
```json
// package.json中锁定版本
{
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  }
}
```

### 3. 错误监控
```javascript
// 全局错误处理
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});
```

### 4. 日志记录
```javascript
// 使用winston记录日志
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

### 5. 自动化测试
```bash
# 添加测试脚本
npm install --save-dev jest supertest
npm test
```

## 📊 问题优先级和影响分析

| 问题类型 | 影响程度 | 解决难度 | 优先级 |
|---------|---------|---------|--------|
| Node.js环境 | 高 | 低 | P0 |
| 端口占用 | 中 | 低 | P1 |
| 权限问题 | 中 | 中 | P1 |
| 依赖冲突 | 高 | 中 | P0 |
| 网络问题 | 中 | 高 | P2 |
| 跨平台兼容 | 低 | 中 | P3 |

## 🎯 总结和建议

1. **建立标准化开发环境** - 使用Docker或虚拟机确保环境一致性
2. **完善错误处理机制** - 添加全局错误捕获和日志记录
3. **自动化部署流程** - 使用CI/CD减少人为错误
4. **定期更新依赖** - 保持依赖包的安全性和兼容性
5. **文档化解决方案** - 记录常见问题和解决方法

通过这些措施，可以显著减少开发过程中的系统运行问题，提高开发效率和项目稳定性。
