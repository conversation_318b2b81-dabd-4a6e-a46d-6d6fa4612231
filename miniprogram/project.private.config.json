{"libVersion": "3.8.8", "projectname": "Shuan-Q%E5%8F%B0%E7%90%83%E6%95%99%E7%BB%83%E9%A2%84%E7%BA%A6%E5%B9%B3%E5%8F%B0", "condition": {}, "setting": {"urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": true, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false, "useIsolateContext": true}}