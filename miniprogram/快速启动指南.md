# 🚀 Shuan-Q 小程序快速启动指南

## ✅ 问题已修复

已移除底部导航栏的图标文件依赖，现在使用纯文字导航。

## 📱 启动步骤

### 1. 打开微信开发者工具
- 选择"导入项目"
- 项目目录：选择 `miniprogram` 文件夹
- AppID：选择"测试号"或输入 `wxtest123456`
- 项目名称：`Shuan-Q台球教练预约平台`

### 2. 配置网络请求
在开发者工具中：
1. 点击右上角"详情"
2. 勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

### 3. 启动后端服务器
```bash
cd backend
NODE_ENV=test node test-server.js
```

### 4. 测试功能
- 登录测试：手机号 `13800138000`，验证码 `123456`
- 浏览课程包和教练信息
- 测试购买和订单功能

## 🎯 功能特色

- ✅ 完整的底部导航栏（纯文字，无需图标文件）
- ✅ 首页展示和统计数据
- ✅ 教练列表和筛选
- ✅ 课程包浏览和购买
- ✅ 订单管理
- ✅ 用户登录和个人中心

## 🔧 如果仍有问题

1. **清除缓存**：在开发者工具中选择"清缓存" -> "清除全部缓存"
2. **重新编译**：点击"编译"按钮重新编译项目
3. **检查控制台**：查看Console面板的错误信息

现在小程序应该可以正常启动了！🎉
