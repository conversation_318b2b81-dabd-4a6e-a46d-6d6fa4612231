#!/bin/bash

# Shuan-Q 台球教练预约平台演示启动脚本

echo "🎱 =============================================="
echo "🎉    Shuan-Q 台球教练预约平台演示启动    🎉"
echo "=============================================="
echo ""

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js"
    echo "📥 下载地址: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"

# 检查依赖
echo "📦 检查项目依赖..."
if [ ! -d "node_modules" ]; then
    echo "📥 正在安装根目录依赖..."
    npm install
fi

if [ ! -d "backend/node_modules" ]; then
    echo "📥 正在安装后端依赖..."
    cd backend && npm install && cd ..
fi

if [ ! -d "mobile-app/node_modules" ]; then
    echo "📥 正在安装移动端依赖..."
    cd mobile-app && npm install && cd ..
fi

echo "✅ 依赖检查完成"

# 检查端口
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口 3000 被占用，正在释放..."
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

echo ""
echo "🚀 启动选项:"
echo "1. 🎮 交互式演示 (推荐)"
echo "2. 🖥️  启动服务器"
echo "3. 🧪 运行功能测试"
echo "4. 📱 移动端演示"
echo "5. 🌐 打开 API 文档"
echo ""

read -p "请选择启动方式 (1-5): " choice

case $choice in
    1)
        echo "🎮 启动交互式演示..."
        echo "📝 提示: 服务器将在后台自动启动"
        
        # 后台启动服务器
        cd backend && NODE_ENV=test node test-server.js &
        SERVER_PID=$!
        cd ..
        
        # 等待服务器启动
        sleep 3
        
        # 启动演示
        node demo.js
        
        # 清理
        kill $SERVER_PID 2>/dev/null || true
        ;;
    2)
        echo "🖥️  启动服务器..."
        echo "📖 API 地址: http://localhost:3000"
        echo "🏥 健康检查: http://localhost:3000/health"
        echo "📋 按 Ctrl+C 停止服务器"
        cd backend && NODE_ENV=test node test-server.js
        ;;
    3)
        echo "🧪 运行功能测试..."
        
        # 后台启动服务器
        cd backend && NODE_ENV=test node test-server.js &
        SERVER_PID=$!
        cd ..
        
        # 等待服务器启动
        sleep 3
        
        # 运行测试
        echo "📚 运行课程包功能测试..."
        cd backend && NODE_ENV=test node test-packages.js
        cd ..
        
        echo ""
        echo "📱 运行移动端演示..."
        cd mobile-app && NODE_ENV=test node test-packages-app.js
        cd ..
        
        # 清理
        kill $SERVER_PID 2>/dev/null || true
        ;;
    4)
        echo "📱 移动端演示..."
        
        # 后台启动服务器
        cd backend && NODE_ENV=test node test-server.js &
        SERVER_PID=$!
        cd ..
        
        # 等待服务器启动
        sleep 3
        
        # 运行移动端演示
        cd mobile-app && NODE_ENV=test node test-packages-app.js
        cd ..
        
        # 清理
        kill $SERVER_PID 2>/dev/null || true
        ;;
    5)
        echo "🌐 打开 API 文档..."
        
        # 后台启动服务器
        cd backend && NODE_ENV=test node test-server.js &
        SERVER_PID=$!
        cd ..
        
        # 等待服务器启动
        sleep 3
        
        echo "📖 API 文档地址: http://localhost:3000"
        
        # 尝试打开浏览器
        if command -v open &> /dev/null; then
            open http://localhost:3000
        elif command -v xdg-open &> /dev/null; then
            xdg-open http://localhost:3000
        else
            echo "请手动打开浏览器访问: http://localhost:3000"
        fi
        
        echo "📋 按回车键停止服务器..."
        read
        
        # 清理
        kill $SERVER_PID 2>/dev/null || true
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🎉 演示结束，感谢体验 Shuan-Q 台球教练预约平台！"
