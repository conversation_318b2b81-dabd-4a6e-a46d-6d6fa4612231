<!--pages/orders/orders.wxml-->
<view class="page-container">
  <view wx:if="{{!isLogin}}" class="login-prompt">
    <view class="empty-icon">🛒</view>
    <text class="empty-text">请先登录查看订单</text>
    <button class="btn btn-primary" bindtap="goToLogin">立即登录</button>
  </view>

  <view wx:else>
    <view wx:if="{{loading}}" class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>

    <view wx:elif="{{orders.length === 0}}" class="empty-state">
      <view class="empty-icon">📋</view>
      <text class="empty-text">暂无订单</text>
      <button class="btn btn-primary" bindtap="goToPackages">去购买课程包</button>
    </view>

    <view wx:else class="orders-list">
      <view wx:for="{{orders}}" wx:key="id" class="order-card">
        <view class="order-header">
          <text class="order-number">订单号：{{item.orderNumber}}</text>
          <view class="order-status status-{{item.status}}">
            {{item.status === 'pending' ? '待支付' : item.status === 'paid' ? '已支付' : '已完成'}}
          </view>
        </view>
        
        <view class="order-content">
          <text class="package-name">{{item.package.name}}</text>
          <text class="order-amount">¥{{item.amount}}</text>
        </view>
        
        <view class="order-footer">
          <text class="order-time">{{item.createdAt}}</text>
          <button wx:if="{{item.status === 'pending'}}" class="btn btn-primary btn-small" bindtap="payOrder" data-id="{{item.id}}">
            立即支付
          </button>
        </view>
      </view>
    </view>
  </view>
</view>
