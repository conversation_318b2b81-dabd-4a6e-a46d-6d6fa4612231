// pages/login/login.js
const app = getApp();

Page({
  data: {
    phone: '13800138000',
    code: '',
    codeLoading: false,
    loginLoading: false,
    countdown: 0
  },

  onPhoneInput(e) {
    this.setData({
      phone: e.detail.value
    });
  },

  onCodeInput(e) {
    this.setData({
      code: e.detail.value
    });
  },

  async sendCode() {
    const { phone } = this.data;
    
    if (!phone || phone.length !== 11) {
      app.showToast('请输入正确的手机号');
      return;
    }

    this.setData({ codeLoading: true });

    try {
      await app.request({
        url: '/auth/send-code',
        method: 'POST',
        data: { phone }
      });

      app.showToast('验证码发送成功', 'success');
      this.setData({ code: '123456' }); // 测试环境自动填入
      this.startCountdown();

    } catch (error) {
      console.error('发送验证码失败:', error);
      app.showToast('发送验证码失败');
    } finally {
      this.setData({ codeLoading: false });
    }
  },

  startCountdown() {
    let countdown = 60;
    this.setData({ countdown });

    const timer = setInterval(() => {
      countdown--;
      this.setData({ countdown });

      if (countdown <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  },

  async login() {
    const { phone, code } = this.data;

    if (!phone || !code) {
      app.showToast('请输入手机号和验证码');
      return;
    }

    this.setData({ loginLoading: true });

    try {
      const res = await app.request({
        url: '/auth/login',
        method: 'POST',
        data: { phone, code }
      });

      const { user, token } = res.data;
      app.login(user, token);
      
      app.showToast('登录成功', 'success');
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('登录失败:', error);
      app.showToast(error.message || '登录失败');
    } finally {
      this.setData({ loginLoading: false });
    }
  }
});
