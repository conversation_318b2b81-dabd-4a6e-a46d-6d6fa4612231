# MCP Feedback Enhanced - 快速参考

## 🚀 快速启动命令

```bash
# 测试 Web UI
uvx mcp-feedback-enhanced@latest test --web

# 测试桌面应用
uvx mcp-feedback-enhanced@latest test --desktop

# 查看版本
uvx mcp-feedback-enhanced@latest version
```

## 📁 配置文件

### Web UI 模式
文件: `mcp-config-web.json`
端口: 8765

### 桌面应用模式  
文件: `mcp-config-desktop.json`
端口: 8765 (桌面应用)

## 🔧 环境变量

- `MCP_DEBUG`: `false` (生产环境)
- `MCP_WEB_PORT`: `8765` (默认端口)
- `MCP_DESKTOP_MODE`: `true` (桌面模式)

## 💡 使用提示

1. **选择模式**: 本地开发用桌面应用，远程开发用 Web UI
2. **配置 AI**: 将配置文件添加到 Cursor 等 AI 助手中
3. **设置规则**: 添加提示词规则以获得最佳体验
4. **安全开发**: AI 会在执行操作前征求确认

## 🛠️ 针对 Shuan-Q 项目

- ✅ 避免 npm/npx 环境问题
- ✅ 代码修改前确认
- ✅ 依赖安装前询问
- ✅ 调试时提供选择

## 📞 支持

- GitHub: https://github.com/Minidoracat/mcp-feedback-enhanced
- 详细文档: `MCP-FEEDBACK-ENHANCED-安装指南.md`
