<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #5a9178;
        }
        button {
            background: #5a9178;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #4a7c59;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin-top: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }
        .input-group {
            display: flex;
            gap: 10px;
        }
        .input-group input {
            flex: 1;
        }
        .input-group button {
            width: auto;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Shuan-Q 登录调试工具</h1>
        
        <div style="margin-bottom: 20px;">
            <strong>服务器状态:</strong>
            <span id="serverStatus" class="status offline">检查中...</span>
        </div>
        
        <div class="form-group">
            <label for="phone">手机号:</label>
            <input type="tel" id="phone" value="13800138000" readonly>
        </div>
        
        <div class="form-group">
            <label for="code">验证码:</label>
            <div class="input-group">
                <input type="text" id="code" placeholder="请输入验证码">
                <button onclick="sendCode()" id="sendBtn">获取验证码</button>
            </div>
            <small style="color: #666;">测试环境验证码: 123456</small>
        </div>
        
        <button onclick="login()" id="loginBtn">登录</button>
        <button onclick="testAPI()">测试API连接</button>
        <button onclick="clearLog()">清空日志</button>
        
        <div id="result"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        let authToken = null;
        let currentUser = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('result').innerHTML = '';
        }
        
        async function checkServerStatus() {
            try {
                log('检查服务器状态...');
                const response = await fetch('http://localhost:3000/', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 服务器在线: ${data.message} v${data.version}`);
                    document.getElementById('serverStatus').textContent = '在线';
                    document.getElementById('serverStatus').className = 'status online';
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ 服务器离线: ${error.message}`);
                document.getElementById('serverStatus').textContent = '离线';
                document.getElementById('serverStatus').className = 'status offline';
                return false;
            }
        }
        
        async function testAPI() {
            log('=== 开始API连接测试 ===');
            
            // 测试根路径
            try {
                const response = await fetch('http://localhost:3000/');
                const data = await response.json();
                log(`✅ 根路径测试成功: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log(`❌ 根路径测试失败: ${error.message}`);
                return;
            }
            
            // 测试CORS
            try {
                const response = await fetch(`${API_BASE}/coaches`, {
                    method: 'GET',
                    mode: 'cors'
                });
                const data = await response.json();
                log(`✅ CORS测试成功，获取到 ${data.data.coaches.length} 个教练`);
            } catch (error) {
                log(`❌ CORS测试失败: ${error.message}`);
            }
        }
        
        async function sendCode() {
            const phone = document.getElementById('phone').value;
            const btn = document.getElementById('sendBtn');
            
            btn.disabled = true;
            btn.textContent = '发送中...';
            log('=== 开始发送验证码 ===');
            
            try {
                log(`请求URL: ${API_BASE}/auth/send-code`);
                log(`请求方法: POST`);
                log(`请求头: Content-Type: application/json`);
                log(`请求体: ${JSON.stringify({ phone })}`);
                
                const response = await fetch(`${API_BASE}/auth/send-code`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    mode: 'cors',
                    body: JSON.stringify({ phone })
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                log(`响应头: ${JSON.stringify([...response.headers.entries()])}`);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`响应错误内容: ${errorText}`);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    showResult('✅ 验证码发送成功！', 'success');
                    document.getElementById('code').value = '123456';
                    log('✅ 验证码发送成功，自动填入123456');
                } else {
                    showResult(`❌ 发送失败: ${data.error}`, 'error');
                    log(`❌ 发送失败: ${data.error}`);
                }
            } catch (error) {
                log(`❌ 发送验证码异常: ${error.message}`);
                log(`错误堆栈: ${error.stack}`);
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '获取验证码';
            }
        }
        
        async function login() {
            const phone = document.getElementById('phone').value;
            const code = document.getElementById('code').value;
            const btn = document.getElementById('loginBtn');
            
            if (!code) {
                showResult('请输入验证码', 'error');
                log('❌ 验证码为空');
                return;
            }
            
            btn.disabled = true;
            btn.textContent = '登录中...';
            log('=== 开始登录流程 ===');
            
            try {
                log(`请求URL: ${API_BASE}/auth/login`);
                log(`请求方法: POST`);
                log(`请求头: Content-Type: application/json`);
                log(`请求体: ${JSON.stringify({ phone, code })}`);
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    mode: 'cors',
                    body: JSON.stringify({ phone, code })
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                log(`响应头: ${JSON.stringify([...response.headers.entries()])}`);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`响应错误内容: ${errorText}`);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    authToken = data.data.token;
                    currentUser = data.data.user;
                    
                    log(`✅ 登录成功！`);
                    log(`Token: ${authToken}`);
                    log(`用户信息: ${JSON.stringify(currentUser, null, 2)}`);
                    
                    showResult(`🎉 登录成功！\n用户: ${currentUser.nickname}\n手机: ${currentUser.phone}\nToken: ${authToken.substring(0, 30)}...`, 'success');
                } else {
                    log(`❌ 登录失败: ${data.error}`);
                    showResult(`❌ 登录失败: ${data.error}`, 'error');
                }
            } catch (error) {
                log(`❌ 登录异常: ${error.message}`);
                log(`错误堆栈: ${error.stack}`);
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '登录';
            }
        }
        
        // 页面加载时检查服务器状态
        window.onload = function() {
            log('页面加载完成');
            checkServerStatus();
        };
    </script>
</body>
</html>
