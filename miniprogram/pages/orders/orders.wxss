/* pages/orders/orders.wxss */

.login-prompt {
  text-align: center;
  padding: 120rpx 40rpx;
}

.orders-list {
  padding: 20rpx;
}

.order-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.order-number {
  font-size: 24rpx;
  color: #666;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-paid {
  background: #d1edff;
  color: #0c5460;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.package-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.order-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #e74c3c;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}
