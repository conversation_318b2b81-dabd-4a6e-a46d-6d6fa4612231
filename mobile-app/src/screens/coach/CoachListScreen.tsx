import React from "react"; import { View, Text, FlatList, StyleSheet } from "react-native"; export default function CoachListScreen() { const coaches = [{ id: "1", name: "张教练", rating: 4.9, specialties: ["斯诺克", "九球"] }]; return ( <View style={styles.container}> <Text style={styles.title}>教练列表</Text> <FlatList data={coaches} keyExtractor={(item) => item.id} renderItem={({ item }) => ( <View style={styles.coachCard}> <Text style={styles.coachName}>{item.name}</Text> <Text>评分: {item.rating}</Text> </View> )} /> </View> ); } const styles = StyleSheet.create({ container: { flex: 1, padding: 20 }, title: { fontSize: 24, fontWeight: "bold", marginBottom: 20 }, coachCard: { backgroundColor: "white", padding: 15, marginBottom: 10, borderRadius: 8, shadowColor: "#000", shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3 }, coachName: { fontSize: 18, fontWeight: "bold", marginBottom: 5 } });
