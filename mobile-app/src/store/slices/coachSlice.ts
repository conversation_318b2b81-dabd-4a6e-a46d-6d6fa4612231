import { createSlice, PayloadAction } from "@reduxjs/toolkit"; interface Coach { id: string; name: string; avatar: string; rating: number; specialties: string[]; location: string; } interface CoachState { coaches: Coach[]; loading: boolean; selectedCoach: Coach | null; } const initialState: CoachState = { coaches: [], loading: false, selectedCoach: null }; const coachSlice = createSlice({ name: "coach", initialState, reducers: { setCoaches: (state, action: PayloadAction<Coach[]>) => { state.coaches = action.payload; }, setLoading: (state, action: PayloadAction<boolean>) => { state.loading = action.payload; }, setSelectedCoach: (state, action: PayloadAction<Coach | null>) => { state.selectedCoach = action.payload; } } }); export const { setCoaches, setLoading, setSelectedCoach } = coachSlice.actions; export default coachSlice.reducer;
