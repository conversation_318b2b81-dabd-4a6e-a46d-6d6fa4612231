<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>-<PERSON> 台球教练预约平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #5a9178;
            --secondary-color: #f8f9fa;
            --accent-color: #e74c3c;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--secondary-color);
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), #4a7c59);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .feature-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .coach-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .package-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .package-card .badge-discount {
            position: absolute;
            top: 15px;
            right: 15px;
            background: var(--accent-color);
        }
        
        .price-original {
            text-decoration: line-through;
            color: #999;
            font-size: 0.9em;
        }
        
        .price-current {
            color: var(--accent-color);
            font-weight: bold;
            font-size: 1.5em;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #4a7c59;
            border-color: #4a7c59;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-paid { background-color: #d1edff; color: #0c5460; }
        .status-completed { background-color: #d4edda; color: #155724; }
        
        .demo-section {
            padding: 60px 0;
        }
        
        .api-endpoint {
            background: #f8f9fa;
            border-left: 4px solid var(--primary-color);
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .loading {
            display: none;
        }
        
        .alert-demo {
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-circle text-success me-2"></i>
                Shuan-Q 台球教练预约平台
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">功能特色</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#coaches">教练团队</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#packages">课程包</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#demo">在线演示</a>
                    </li>
                    <li class="nav-item">
                        <button class="btn btn-primary ms-2" onclick="showLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="container">
            <h1 class="display-4 mb-4">🎱 专业台球教练预约平台</h1>
            <p class="lead mb-4">连接优秀教练与学员，提供专业的台球教学服务</p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <h3><i class="fas fa-users text-warning"></i> 2+</h3>
                            <p>专业教练</p>
                        </div>
                        <div class="col-md-4">
                            <h3><i class="fas fa-graduation-cap text-warning"></i> 2+</h3>
                            <p>精品课程包</p>
                        </div>
                        <div class="col-md-4">
                            <h3><i class="fas fa-star text-warning"></i> 4.8</h3>
                            <p>平均评分</p>
                        </div>
                    </div>
                </div>
            </div>
            <button class="btn btn-light btn-lg mt-4" onclick="startDemo()">
                <i class="fas fa-play me-2"></i>开始体验
            </button>
        </div>
    </section>

    <!-- 功能特色 -->
    <section id="features" class="demo-section">
        <div class="container">
            <h2 class="text-center mb-5">🚀 功能特色</h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card feature-card">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                            <h5>移动端优先</h5>
                            <p class="text-muted">React Native 跨平台应用，iOS 和 Android 完美支持</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card feature-card">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                            <h5>安全认证</h5>
                            <p class="text-muted">JWT Token 认证，手机号验证码登录，保障账户安全</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card feature-card">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-credit-card fa-3x text-warning mb-3"></i>
                            <h5>便捷支付</h5>
                            <p class="text-muted">支持多种支付方式，订单管理，退款处理</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 教练团队 -->
    <section id="coaches" class="demo-section bg-white">
        <div class="container">
            <h2 class="text-center mb-5">👨‍🏫 优秀教练团队</h2>
            <div class="row" id="coaches-list">
                <!-- 教练列表将通过 JavaScript 动态加载 -->
            </div>
        </div>
    </section>

    <!-- 课程包 -->
    <section id="packages" class="demo-section">
        <div class="container">
            <h2 class="text-center mb-5">📚 精品课程包</h2>
            <div class="row" id="packages-list">
                <!-- 课程包列表将通过 JavaScript 动态加载 -->
            </div>
        </div>
    </section>

    <!-- 在线演示 -->
    <section id="demo" class="demo-section bg-white">
        <div class="container">
            <h2 class="text-center mb-5">🎮 在线演示</h2>
            
            <!-- 登录演示 -->
            <div class="row mb-5">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-sign-in-alt me-2"></i>用户登录演示</h5>
                        </div>
                        <div class="card-body">
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label class="form-label">手机号</label>
                                    <input type="tel" class="form-control" id="phone" value="13800138000" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">验证码</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="code" placeholder="请输入验证码">
                                        <button class="btn btn-outline-secondary" type="button" onclick="sendCode()">
                                            获取验证码
                                        </button>
                                    </div>
                                    <small class="text-muted">测试环境验证码: 123456</small>
                                </div>
                                <button type="button" class="btn btn-primary" onclick="login()">
                                    <span class="loading spinner-border spinner-border-sm me-2"></span>
                                    登录
                                </button>
                            </form>
                            <div id="loginResult" class="mt-3"></div>
                        </div>
                    </div>
                </div>
                
                <!-- API 接口展示 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-code me-2"></i>API 接口</h5>
                        </div>
                        <div class="card-body">
                            <div class="api-endpoint">
                                <strong>POST</strong> /api/auth/send-code
                                <br><small>发送验证码</small>
                            </div>
                            <div class="api-endpoint">
                                <strong>POST</strong> /api/auth/login
                                <br><small>用户登录</small>
                            </div>
                            <div class="api-endpoint">
                                <strong>GET</strong> /api/coaches
                                <br><small>获取教练列表</small>
                            </div>
                            <div class="api-endpoint">
                                <strong>GET</strong> /api/packages
                                <br><small>获取课程包列表</small>
                            </div>
                            <div class="api-endpoint">
                                <strong>POST</strong> /api/orders
                                <br><small>创建订单</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的订单 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-shopping-cart me-2"></i>我的订单</h5>
                        </div>
                        <div class="card-body">
                            <div id="orders-list">
                                <p class="text-muted text-center">请先登录查看订单</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p>&copy; 2024 Shuan-Q 台球教练预约平台. 技术演示项目.</p>
            <p>
                <i class="fab fa-node-js me-2"></i>Node.js + Express
                <i class="fab fa-react ms-3 me-2"></i>React Native
                <i class="fas fa-database ms-3 me-2"></i>SQLite
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
