{"name": "shuan-q", "version": "1.0.0", "description": "台球教练预约平台", "private": true, "workspaces": ["mobile-app", "backend", "shared"], "scripts": {"install:all": "npm install && npm run install:mobile && npm run install:backend", "install:mobile": "cd mobile-app && npm install", "install:backend": "cd backend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:mobile\"", "dev:mobile": "cd mobile-app && npm run start", "dev:backend": "cd backend && npm run dev", "build": "npm run build:backend && npm run build:mobile", "build:mobile": "cd mobile-app && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:backend && npm run test:mobile", "test:mobile": "cd mobile-app && npm test", "test:backend": "cd backend && npm test", "lint": "npm run lint:mobile && npm run lint:backend", "lint:mobile": "cd mobile-app && npm run lint", "lint:backend": "cd backend && npm run lint", "clean": "npm run clean:mobile && npm run clean:backend", "clean:mobile": "cd mobile-app && rm -rf node_modules && rm -rf ios/build && rm -rf android/build", "clean:backend": "cd backend && rm -rf node_modules && rm -rf dist"}, "devDependencies": {"concurrently": "^8.2.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "prettier": "^3.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/shuan-q.git"}, "keywords": ["billiards", "coach", "appointment", "react-native", "mobile-app"], "author": "Your Name", "license": "MIT"}