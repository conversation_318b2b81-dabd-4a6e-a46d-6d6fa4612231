<!--pages/packages/packages.wxml-->
<view class="page-container">
  <!-- 分类筛选 -->
  <view class="category-bar">
    <scroll-view scroll-x="true" class="category-scroll">
      <view class="category-list">
        <view 
          wx:for="{{categories}}" 
          wx:key="value"
          class="category-item {{selectedCategory === item.value ? 'active' : ''}}"
          bindtap="onCategoryChange"
          data-value="{{item.value}}"
        >
          <text class="category-icon">{{item.icon}}</text>
          <text class="category-text">{{item.label}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 课程包列表 -->
  <view class="packages-container">
    <view wx:if="{{loading}}" class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>

    <view wx:elif="{{packages.length === 0}}" class="empty-state">
      <view class="empty-icon">📚</view>
      <text class="empty-text">暂无课程包</text>
      <button class="btn btn-primary" bindtap="onRefresh">刷新</button>
    </view>

    <view wx:else class="packages-list">
      <view 
        wx:for="{{packages}}" 
        wx:key="id"
        class="package-card"
        bindtap="goToPackageDetail"
        data-id="{{item.id}}"
      >
        <!-- 优惠标签 -->
        <view wx:if="{{item.originalPrice > item.price}}" class="discount-badge">
          省{{Math.round((1 - item.price / item.originalPrice) * 100)}}%
        </view>

        <!-- 课程包头部 -->
        <view class="package-header">
          <text class="package-name">{{item.name}}</text>
          <view class="package-level badge-{{item.level}}">
            {{item.level === 'beginner' ? '初级' : item.level === 'intermediate' ? '中级' : '高级'}}
          </view>
        </view>

        <!-- 课程包描述 -->
        <text class="package-desc">{{item.description}}</text>

        <!-- 课程包信息 -->
        <view class="package-info">
          <view class="info-item">
            <text class="info-icon">📚</text>
            <text class="info-text">{{item.totalSessions}}课时</text>
          </view>
          <view class="info-item">
            <text class="info-icon">⏰</text>
            <text class="info-text">{{item.validityDays}}天有效</text>
          </view>
          <view class="info-item">
            <text class="info-icon">🏷️</text>
            <text class="info-text">{{item.category}}</text>
          </view>
        </view>

        <!-- 课程特色 -->
        <view class="package-features">
          <view 
            wx:for="{{item.features}}" 
            wx:for-item="feature"
            wx:for-index="featureIndex"
            wx:key="featureIndex"
            class="feature-item"
            wx:if="{{featureIndex < 2}}"
          >
            <text class="feature-icon">✓</text>
            <text class="feature-text">{{feature}}</text>
          </view>
        </view>

        <!-- 教练信息 -->
        <view wx:if="{{item.coach}}" class="package-coach">
          <view class="coach-avatar-small">
            <text class="avatar-text-small">{{item.coach.user.nickname.charAt(0)}}</text>
          </view>
          <view class="coach-info-small">
            <text class="coach-name-small">{{item.coach.user.nickname}}</text>
            <text class="coach-rating-small">⭐ {{item.coach.rating}}</text>
          </view>
        </view>

        <!-- 价格和购买 -->
        <view class="package-footer">
          <view class="package-price">
            <text wx:if="{{item.originalPrice > item.price}}" class="price-original">¥{{item.originalPrice}}</text>
            <text class="price-current">¥{{item.price}}</text>
          </view>
          <button 
            class="btn btn-primary package-buy-btn" 
            bindtap="buyPackage" 
            data-id="{{item.id}}"
          >
            立即购买
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{hasMore && !loading}}" class="load-more">
    <button class="btn btn-outline" bindtap="loadMore">加载更多</button>
  </view>
</view>
