// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: 'http://localhost:3000/api', // 开发环境API地址
    isLogin: false
  },

  onLaunch() {
    console.log('🎱 Shuan-Q 小程序启动');
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 获取系统信息
    this.getSystemInfo();
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
      this.globalData.isLogin = true;
      console.log('用户已登录:', userInfo.nickname);
    } else {
      console.log('用户未登录');
    }
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res;
        console.log('系统信息:', res);
      }
    });
  },

  // 登录方法
  login(userInfo, token) {
    this.globalData.userInfo = userInfo;
    this.globalData.token = token;
    this.globalData.isLogin = true;
    
    // 保存到本地存储
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('token', token);
    
    console.log('登录成功:', userInfo.nickname);
  },

  // 退出登录
  logout() {
    this.globalData.userInfo = null;
    this.globalData.token = null;
    this.globalData.isLogin = false;
    
    // 清除本地存储
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('token');
    
    console.log('已退出登录');
  },

  // 网络请求封装
  request(options) {
    const { url, method = 'GET', data = {}, header = {} } = options;
    
    // 添加token到请求头
    if (this.globalData.token) {
      header.Authorization = `Bearer ${this.globalData.token}`;
    }
    
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.globalData.baseUrl + url,
        method,
        data,
        header: {
          'Content-Type': 'application/json',
          ...header
        },
        success: (res) => {
          console.log('API请求成功:', url, res.data);
          if (res.data.success) {
            resolve(res.data);
          } else {
            reject(new Error(res.data.error || '请求失败'));
          }
        },
        fail: (err) => {
          console.error('API请求失败:', url, err);
          reject(err);
        }
      });
    });
  },

  // 显示提示信息
  showToast(title, icon = 'none') {
    wx.showToast({
      title,
      icon,
      duration: 2000
    });
  },

  // 显示加载中
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  // 隐藏加载
  hideLoading() {
    wx.hideLoading();
  }
});
