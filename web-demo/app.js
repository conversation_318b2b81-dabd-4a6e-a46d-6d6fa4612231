// Shuan-Q 台球教练预约平台 - Web 演示
const API_BASE = 'http://localhost:3000/api';
let authToken = null;
let currentUser = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadCoaches();
    loadPackages();
    checkServerStatus();
});

// 检查服务器状态
async function checkServerStatus() {
    try {
        const response = await fetch('http://localhost:3000/');
        if (response.ok) {
            showAlert('success', '✅ 服务器连接正常，所有功能可用');
        }
    } catch (error) {
        showAlert('warning', '⚠️ 服务器未启动，请先运行: cd backend && NODE_ENV=test node test-server.js');
    }
}

// 显示提示信息
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-demo`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 5秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 发送验证码
async function sendCode() {
    const phone = document.getElementById('phone').value;

    try {
        console.log('发送验证码请求:', { phone });

        const response = await fetch(`${API_BASE}/auth/send-code`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            mode: 'cors',
            body: JSON.stringify({ phone })
        });

        console.log('验证码响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('验证码响应数据:', data);

        if (data.success) {
            showAlert('success', '✅ 验证码发送成功 (测试环境固定为: 123456)');
            document.getElementById('code').value = '123456';
        } else {
            showAlert('danger', `❌ ${data.error}`);
        }
    } catch (error) {
        console.error('发送验证码错误:', error);
        showAlert('danger', `❌ 网络错误: ${error.message}`);
    }
}

// 用户登录
async function login() {
    console.log('登录函数被调用');

    const phone = document.getElementById('phone').value;
    const code = document.getElementById('code').value;
    const loginBtn = document.querySelector('#loginForm button');
    const loading = loginBtn.querySelector('.loading');

    console.log('获取到的值:', { phone, code });

    if (!code) {
        showAlert('warning', '请输入验证码');
        return;
    }

    // 显示加载状态
    if (loading) {
        loading.style.display = 'inline-block';
    }
    loginBtn.disabled = true;
    loginBtn.textContent = '登录中...';

    try {
        console.log('发送登录请求:', { phone, code });
        console.log('API地址:', `${API_BASE}/auth/login`);

        const response = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            mode: 'cors',
            body: JSON.stringify({ phone, code })
        });

        console.log('登录响应状态:', response.status);
        console.log('登录响应头:', response.headers);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('响应错误内容:', errorText);
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('登录响应数据:', data);

        if (data.success) {
            authToken = data.data.token;
            currentUser = data.data.user;

            console.log('登录成功，设置token:', authToken);
            console.log('用户信息:', currentUser);

            showAlert('success', `🎉 登录成功！欢迎 ${currentUser.nickname}`);

            // 更新登录结果显示
            const loginResult = document.getElementById('loginResult');
            if (loginResult) {
                loginResult.innerHTML = `
                    <div class="alert alert-success alert-demo">
                        <h6>✅ 登录成功</h6>
                        <p><strong>用户:</strong> ${currentUser.nickname}</p>
                        <p><strong>手机:</strong> ${currentUser.phone}</p>
                        <p><strong>类型:</strong> ${currentUser.userType}</p>
                        <p><strong>Token:</strong> ${authToken.substring(0, 20)}...</p>
                    </div>
                `;
            }

            // 更新导航栏登录按钮
            updateNavbarLoginButton();

            // 加载用户订单
            setTimeout(() => {
                loadMyOrders();
            }, 1000);

        } else {
            console.error('登录失败:', data.error);
            showAlert('danger', `❌ 登录失败: ${data.error}`);
        }
    } catch (error) {
        console.error('登录错误:', error);
        showAlert('danger', `❌ 网络错误: ${error.message}`);
    } finally {
        if (loading) {
            loading.style.display = 'none';
        }
        loginBtn.disabled = false;
        loginBtn.innerHTML = `
            <span class="loading spinner-border spinner-border-sm me-2" style="display: none;"></span>
            登录
        `;
    }
}

// 加载教练列表
async function loadCoaches() {
    try {
        const response = await fetch(`${API_BASE}/coaches`);
        const data = await response.json();
        
        if (data.success) {
            const coachesContainer = document.getElementById('coaches-list');
            coachesContainer.innerHTML = '';
            
            data.data.coaches.forEach(coach => {
                const coachCard = createCoachCard(coach);
                coachesContainer.appendChild(coachCard);
            });
        }
    } catch (error) {
        console.error('加载教练列表失败:', error);
    }
}

// 创建教练卡片
function createCoachCard(coach) {
    const div = document.createElement('div');
    div.className = 'col-md-6 mb-4';
    div.innerHTML = `
        <div class="card coach-card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="card-title">
                            <i class="fas fa-user-tie me-2 text-primary"></i>
                            ${coach.name}
                        </h5>
                        <p class="text-muted mb-2">${coach.description}</p>
                        <div class="mb-2">
                            <span class="badge bg-primary me-1">
                                <i class="fas fa-star me-1"></i>${coach.rating}
                            </span>
                            <span class="badge bg-secondary">
                                ${coach.ratingCount}人评价
                            </span>
                        </div>
                        <p class="mb-1">
                            <i class="fas fa-map-marker-alt me-2 text-danger"></i>
                            ${coach.location}
                        </p>
                        <p class="mb-1">
                            <i class="fas fa-tags me-2 text-success"></i>
                            ${coach.specialties.join(', ')}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="price-current">¥${coach.hourlyRate}</div>
                        <small class="text-muted">每小时</small>
                        <br>
                        <button class="btn btn-primary btn-sm mt-2" onclick="bookCoach('${coach.id}')">
                            <i class="fas fa-calendar-plus me-1"></i>预约
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    return div;
}

// 加载课程包列表
async function loadPackages() {
    try {
        const response = await fetch(`${API_BASE}/packages`);
        const data = await response.json();
        
        if (data.success) {
            const packagesContainer = document.getElementById('packages-list');
            packagesContainer.innerHTML = '';
            
            data.data.packages.forEach(pkg => {
                const packageCard = createPackageCard(pkg);
                packagesContainer.appendChild(packageCard);
            });
        }
    } catch (error) {
        console.error('加载课程包列表失败:', error);
    }
}

// 创建课程包卡片
function createPackageCard(pkg) {
    const div = document.createElement('div');
    div.className = 'col-md-6 mb-4';
    
    const discount = pkg.originalPrice > pkg.price ? 
        Math.round((1 - pkg.price / pkg.originalPrice) * 100) : 0;
    
    div.innerHTML = `
        <div class="card package-card">
            ${discount > 0 ? `<span class="badge badge-discount">省${discount}%</span>` : ''}
            <div class="card-body">
                <h5 class="card-title">${pkg.name}</h5>
                <p class="card-text text-muted">${pkg.description}</p>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">课时数量</small>
                        <div><strong>${pkg.totalSessions}节</strong></div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">有效期</small>
                        <div><strong>${pkg.validityDays}天</strong></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <span class="badge bg-success me-1">${pkg.category}</span>
                    <span class="badge bg-info">${getLevelText(pkg.level)}</span>
                </div>
                
                <div class="mb-3">
                    ${pkg.features.slice(0, 2).map(feature => 
                        `<small class="d-block text-muted">• ${feature}</small>`
                    ).join('')}
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        ${pkg.originalPrice > pkg.price ? 
                            `<span class="price-original">¥${pkg.originalPrice}</span><br>` : ''}
                        <span class="price-current">¥${pkg.price}</span>
                    </div>
                    <button class="btn btn-primary" onclick="purchasePackage('${pkg.id}')">
                        <i class="fas fa-shopping-cart me-1"></i>购买
                    </button>
                </div>
                
                ${pkg.coach ? `
                    <hr>
                    <small class="text-muted">
                        <i class="fas fa-user-tie me-1"></i>
                        教练: ${pkg.coach.user?.nickname || '未知'}
                        <span class="ms-2">
                            <i class="fas fa-star text-warning"></i>
                            ${pkg.coach.rating}
                        </span>
                    </small>
                ` : ''}
            </div>
        </div>
    `;
    return div;
}

// 获取级别文本
function getLevelText(level) {
    const levelMap = {
        'beginner': '初级',
        'intermediate': '中级',
        'advanced': '高级'
    };
    return levelMap[level] || level;
}

// 购买课程包
async function purchasePackage(packageId) {
    if (!authToken) {
        showAlert('warning', '请先登录后再购买课程包');
        return;
    }
    
    try {
        // 创建订单
        const orderResponse = await fetch(`${API_BASE}/orders`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                packageId,
                paymentMethod: 'wechat'
            })
        });
        
        const orderData = await orderResponse.json();
        
        if (orderData.success) {
            const order = orderData.data;
            showAlert('success', `✅ 订单创建成功！订单号: ${order.orderNumber}`);
            
            // 模拟支付
            setTimeout(async () => {
                try {
                    const payResponse = await fetch(`${API_BASE}/orders/${order.id}/pay`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });
                    
                    const payData = await payResponse.json();
                    
                    if (payData.success) {
                        showAlert('success', '🎉 支付成功！课程包购买完成');
                        loadMyOrders(); // 刷新订单列表
                    }
                } catch (error) {
                    showAlert('danger', `支付失败: ${error.message}`);
                }
            }, 2000);
            
        } else {
            showAlert('danger', `订单创建失败: ${orderData.error}`);
        }
    } catch (error) {
        showAlert('danger', `购买失败: ${error.message}`);
    }
}

// 加载我的订单
async function loadMyOrders() {
    if (!authToken) return;
    
    try {
        const response = await fetch(`${API_BASE}/orders/my`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            const ordersContainer = document.getElementById('orders-list');
            
            if (data.data.orders.length === 0) {
                ordersContainer.innerHTML = '<p class="text-muted text-center">暂无订单</p>';
                return;
            }
            
            ordersContainer.innerHTML = '';
            
            data.data.orders.forEach(order => {
                const orderCard = createOrderCard(order);
                ordersContainer.appendChild(orderCard);
            });
        }
    } catch (error) {
        console.error('加载订单失败:', error);
    }
}

// 创建订单卡片
function createOrderCard(order) {
    const div = document.createElement('div');
    div.className = 'mb-3';
    
    const statusClass = {
        'pending': 'status-pending',
        'paid': 'status-paid',
        'completed': 'status-completed'
    }[order.status] || 'status-pending';
    
    const statusText = {
        'pending': '待支付',
        'paid': '已支付',
        'completed': '已完成'
    }[order.status] || order.status;
    
    div.innerHTML = `
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="mb-1">订单号: ${order.orderNumber}</h6>
                        <p class="mb-1">${order.package?.name || '未知课程包'}</p>
                        <small class="text-muted">
                            创建时间: ${new Date(order.createdAt).toLocaleString()}
                        </small>
                        ${order.paidAt ? `
                            <br><small class="text-muted">
                                支付时间: ${new Date(order.paidAt).toLocaleString()}
                            </small>
                        ` : ''}
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="price-current">¥${order.amount}</div>
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    return div;
}

// 预约教练
function bookCoach(coachId) {
    if (!authToken) {
        showAlert('warning', '请先登录后再预约教练');
        return;
    }
    showAlert('info', '预约功能演示 - 请参考完整的移动端应用');
}

// 开始演示
function startDemo() {
    document.getElementById('demo').scrollIntoView({ behavior: 'smooth' });
    showAlert('info', '👋 欢迎体验 Shuan-Q 台球教练预约平台！请先登录体验完整功能');
}

// 更新导航栏登录按钮
function updateNavbarLoginButton() {
    const loginBtn = document.querySelector('.navbar .btn-primary');
    if (loginBtn && currentUser) {
        loginBtn.innerHTML = `
            <i class="fas fa-user me-1"></i>
            ${currentUser.nickname}
        `;
        loginBtn.onclick = () => {
            showAlert('info', `已登录用户: ${currentUser.nickname}`);
        };
    }
}

// 显示登录模态框
function showLogin() {
    document.getElementById('demo').scrollIntoView({ behavior: 'smooth' });
}
