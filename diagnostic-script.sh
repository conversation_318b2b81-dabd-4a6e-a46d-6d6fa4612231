#!/bin/bash

# 🔧 Shuan-Q 项目环境诊断和修复脚本
# 用于检测和修复常见的开发环境问题

echo "🔍 开始 Shuan-Q 项目环境诊断..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${GREEN}✅ $1 已安装${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 未安装${NC}"
        return 1
    fi
}

# 检查版本
check_version() {
    local cmd=$1
    local min_version=$2
    local current_version=$($cmd --version 2>/dev/null | head -n1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -n1)
    
    if [ -n "$current_version" ]; then
        echo -e "${BLUE}📋 $cmd 当前版本: $current_version${NC}"
        # 这里可以添加版本比较逻辑
    else
        echo -e "${YELLOW}⚠️  无法获取 $cmd 版本信息${NC}"
    fi
}

# 检查端口占用
check_port() {
    local port=$1
    if lsof -i :$port &> /dev/null; then
        echo -e "${YELLOW}⚠️  端口 $port 被占用${NC}"
        echo "占用进程:"
        lsof -i :$port
        return 1
    else
        echo -e "${GREEN}✅ 端口 $port 可用${NC}"
        return 0
    fi
}

echo "1. 检查基础环境..."
echo "-------------------"

# 检查 Node.js
if check_command "node"; then
    check_version "node" "18.0.0"
else
    echo -e "${YELLOW}💡 建议安装 Node.js LTS 版本${NC}"
    echo "   macOS: brew install node"
    echo "   Ubuntu: curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash - && sudo apt-get install -y nodejs"
fi

# 检查 npm
if check_command "npm"; then
    check_version "npm" "9.0.0"
else
    echo -e "${YELLOW}💡 npm 通常随 Node.js 一起安装${NC}"
fi

# 检查 npx
if check_command "npx"; then
    echo -e "${GREEN}✅ npx 可用${NC}"
else
    echo -e "${RED}❌ npx 不可用${NC}"
    echo -e "${YELLOW}💡 尝试: npm install -g npx${NC}"
fi

echo ""
echo "2. 检查网络和镜像源..."
echo "----------------------"

# 检查 npm 镜像源
current_registry=$(npm config get registry 2>/dev/null)
echo -e "${BLUE}📋 当前 npm 镜像源: $current_registry${NC}"

if [[ "$current_registry" == *"npmjs.org"* ]]; then
    echo -e "${YELLOW}💡 建议使用国内镜像源提高下载速度:${NC}"
    echo "   npm config set registry https://registry.npmmirror.com/"
fi

# 测试网络连接
echo "测试网络连接..."
if curl -s --max-time 5 https://registry.npmjs.org/ > /dev/null; then
    echo -e "${GREEN}✅ npm 官方源连接正常${NC}"
else
    echo -e "${RED}❌ npm 官方源连接失败${NC}"
fi

echo ""
echo "3. 检查端口占用..."
echo "------------------"

# 检查常用端口
check_port 3000
check_port 8080
check_port 3001

echo ""
echo "4. 检查项目依赖..."
echo "------------------"

# 检查 package.json
if [ -f "package.json" ]; then
    echo -e "${GREEN}✅ package.json 存在${NC}"
    
    # 检查 node_modules
    if [ -d "node_modules" ]; then
        echo -e "${GREEN}✅ node_modules 存在${NC}"
        
        # 检查依赖完整性
        if npm ls &> /dev/null; then
            echo -e "${GREEN}✅ 依赖完整${NC}"
        else
            echo -e "${YELLOW}⚠️  依赖可能不完整，建议运行: npm install${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  node_modules 不存在，需要运行: npm install${NC}"
    fi
else
    echo -e "${RED}❌ package.json 不存在${NC}"
fi

echo ""
echo "5. 检查权限问题..."
echo "------------------"

# 检查当前目录权限
if [ -w "." ]; then
    echo -e "${GREEN}✅ 当前目录可写${NC}"
else
    echo -e "${RED}❌ 当前目录不可写${NC}"
fi

# 检查 npm 全局目录权限
npm_prefix=$(npm config get prefix 2>/dev/null)
if [ -n "$npm_prefix" ] && [ -w "$npm_prefix" ]; then
    echo -e "${GREEN}✅ npm 全局目录权限正常${NC}"
else
    echo -e "${YELLOW}⚠️  npm 全局目录权限可能有问题${NC}"
    echo -e "${YELLOW}💡 解决方案:${NC}"
    echo "   mkdir ~/.npm-global"
    echo "   npm config set prefix '~/.npm-global'"
    echo "   echo 'export PATH=~/.npm-global/bin:\$PATH' >> ~/.bashrc"
fi

echo ""
echo "6. 系统信息..."
echo "-------------"

echo -e "${BLUE}📋 操作系统: $(uname -s)${NC}"
echo -e "${BLUE}📋 架构: $(uname -m)${NC}"
echo -e "${BLUE}📋 当前用户: $(whoami)${NC}"
echo -e "${BLUE}📋 当前目录: $(pwd)${NC}"

# 检查内存使用
if command -v free &> /dev/null; then
    echo -e "${BLUE}📋 内存使用情况:${NC}"
    free -h
elif command -v vm_stat &> /dev/null; then
    echo -e "${BLUE}📋 内存使用情况 (macOS):${NC}"
    vm_stat | head -5
fi

echo ""
echo "7. 快速修复建议..."
echo "------------------"

echo -e "${YELLOW}🔧 常见问题快速修复命令:${NC}"
echo ""
echo "清理 npm 缓存:"
echo "  npm cache clean --force"
echo ""
echo "重新安装依赖:"
echo "  rm -rf node_modules package-lock.json && npm install"
echo ""
echo "修复权限问题:"
echo "  sudo chown -R \$(whoami) ~/.npm"
echo ""
echo "杀死占用端口的进程 (以3000为例):"
echo "  lsof -ti:3000 | xargs kill -9"
echo ""
echo "设置国内镜像源:"
echo "  npm config set registry https://registry.npmmirror.com/"

echo ""
echo "=================================="
echo -e "${GREEN}🎉 诊断完成！${NC}"
echo ""
echo -e "${BLUE}💡 如果问题仍然存在，请检查:${NC}"
echo "1. 防火墙设置"
echo "2. 代理配置"
echo "3. 系统环境变量"
echo "4. IDE/编辑器配置"
echo ""
echo -e "${BLUE}📚 详细解决方案请查看: development-issues-analysis.md${NC}"
