# MCP Feedback Enhanced 安装和使用指南

## 🎯 工具简介

**mcp-feedback-enhanced** 是一个增强版的 MCP (Model Context Protocol) 服务器，专门用于AI辅助开发中的交互式反馈。它可以：

- 🔄 **改善AI开发工作流** - 让AI在执行操作前先征求你的确认，避免盲目执行
- 💰 **降低开发成本** - 通过减少AI的试错次数来降低API调用成本  
- 🖥️ **双界面支持** - Web UI 和桌面应用两种界面
- 📝 **智能交互** - 支持文本输入、图片上传、自动提交等功能

## ✅ 安装状态

### 环境检查
- ✅ Node.js: v22.16.0
- ✅ npm: 10.9.2
- ✅ Python: 3.9.6
- ✅ uv: 0.7.13 (已安装)
- ✅ mcp-feedback-enhanced: 2.5.4 (已测试)

### 测试结果
- ✅ Web UI 界面测试通过 (端口: 9765)
- ✅ 桌面应用程序测试通过 (端口: 8765)
- ✅ 支持 macOS ARM64 架构

## 🚀 使用方法

### 1. 快速测试
```bash
# 测试 Web UI (持续运行模式)
uvx mcp-feedback-enhanced@latest test --web

# 测试桌面应用程序
uvx mcp-feedback-enhanced@latest test --desktop

# 基本功能测试
uvx mcp-feedback-enhanced@latest test
```

### 2. 配置 MCP 服务器

#### Web UI 模式 (推荐用于远程开发)
使用配置文件: `mcp-config-web.json`
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_PORT": "8765"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

#### 桌面应用模式 (推荐用于本地开发)
使用配置文件: `mcp-config-desktop.json`
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DESKTOP_MODE": "true",
        "MCP_WEB_PORT": "8765",
        "MCP_DEBUG": "false"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### 3. 在 AI 助手中配置

将以上配置添加到你的 AI 助手的 MCP 配置中（如 Cursor、Claude Desktop 等）。

### 4. 设置 AI 提示词规则

为了获得最佳效果，建议在 AI 助手中添加以下规则：

```
# MCP Interactive Feedback Rules

1. 在任何过程、任务或对话中，无论是询问、回复还是完成阶段性任务，都必须调用 MCP mcp-feedback-enhanced。
2. 每当收到用户反馈时，如果反馈内容不为空，必须再次调用 MCP mcp-feedback-enhanced 并根据反馈内容调整行为。
3. 只有当用户明确表示"结束"或"不需要更多交互"时，才能停止调用 MCP mcp-feedback-enhanced，过程才算完成。
4. 除非收到终止指令，所有步骤都必须重复调用 MCP mcp-feedback-enhanced。
5. 在完成任务之前，必须使用 MCP mcp-feedback-enhanced 工具询问用户反馈。
```

## 🌟 主要功能

### 📝 智能工作流
- **提示管理**: 常用提示的增删改查、使用统计、智能排序
- **自动定时提交**: 1-86400秒灵活定时器，支持暂停、恢复、取消
- **会话管理与跟踪**: 本地文件存储、隐私控制、历史导出、实时统计
- **连接监控**: WebSocket状态监控、自动重连、质量指示器

### 🖼️ 图片和媒体支持
- **全格式支持**: PNG, JPG, JPEG, GIF, BMP, WebP
- **便捷上传**: 拖拽文件、剪贴板粘贴 (Ctrl+V)
- **无限处理**: 支持任意大小图片，自动智能处理

### 🎨 现代化体验
- **响应式设计**: 适配不同屏幕尺寸，模块化JavaScript架构
- **音频通知**: 内置多种音效，支持自定义音频上传、音量控制
- **智能记忆**: 输入框高度记忆、一键复制、持久化设置
- **多语言支持**: 繁体中文、英文、简体中文，即时切换

## 🔧 环境变量说明

| 变量 | 用途 | 值 | 默认值 |
|------|------|-----|--------|
| `MCP_DEBUG` | 调试模式 | `true`/`false` | `false` |
| `MCP_WEB_PORT` | Web UI 端口 | `1024-65535` | `8765` |
| `MCP_DESKTOP_MODE` | 桌面应用模式 | `true`/`false` | `false` |

## 🛠️ 针对你的项目的建议

基于你的 Shuan-Q 台球教练预约平台项目和之前遇到的 npm/npx 问题，这个工具特别适合：

1. **避免环境问题**: AI 在执行 npm 命令前会先询问你，避免重复之前的环境问题
2. **代码审查**: AI 在修改代码前会展示计划，让你确认后再执行
3. **依赖管理**: 在安装新依赖或修改 package.json 前获得确认
4. **调试协助**: 当遇到错误时，AI 会询问你的偏好解决方案

## 📞 支持和帮助

- **GitHub**: https://github.com/Minidoracat/mcp-feedback-enhanced
- **文档**: 查看项目 README 获取详细信息
- **问题反馈**: 通过 GitHub Issues 报告问题

## 🎉 总结

mcp-feedback-enhanced 已成功安装并测试完成！你现在可以：

1. 选择使用 Web UI 或桌面应用模式
2. 将配置文件添加到你的 AI 助手中
3. 开始享受更智能、更安全的 AI 辅助开发体验

这个工具将帮助你避免之前遇到的 npm/npx 相关问题，让 AI 在执行任何可能有风险的操作前都先征求你的同意。
