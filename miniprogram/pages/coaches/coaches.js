// pages/coaches/coaches.js
const app = getApp();

Page({
  data: {
    coaches: [],
    loading: false,
    hasMore: true,
    page: 1,
    limit: 10,
    searchKeyword: '',
    selectedFilter: 'all',
    filters: [
      { label: '全部', value: 'all' },
      { label: '斯诺克', value: 'snooker' },
      { label: '九球', value: 'nine-ball' },
      { label: '中式八球', value: 'chinese-eight-ball' },
      { label: '高评分', value: 'high-rating' }
    ]
  },

  onLoad() {
    console.log('教练页面加载');
    this.loadCoaches();
  },

  onShow() {
    // 设置底部导航栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
    }
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  // 刷新数据
  onRefresh() {
    this.setData({
      page: 1,
      coaches: [],
      hasMore: true
    });
    this.loadCoaches().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载教练数据
  async loadCoaches(isLoadMore = false) {
    if (this.data.loading) return;
    
    this.setData({ loading: true });

    try {
      const { page, limit, searchKeyword, selectedFilter } = this.data;
      
      const params = {
        page: isLoadMore ? page : 1,
        limit
      };

      // 添加搜索关键词
      if (searchKeyword) {
        params.search = searchKeyword;
      }

      // 添加筛选条件
      if (selectedFilter !== 'all') {
        params.filter = selectedFilter;
      }

      const res = await app.request({
        url: '/coaches',
        method: 'GET',
        data: params
      });

      const newCoaches = res.data.coaches || [];
      const coaches = isLoadMore ? 
        [...this.data.coaches, ...newCoaches] : 
        newCoaches;

      this.setData({
        coaches,
        hasMore: newCoaches.length === limit,
        page: isLoadMore ? page + 1 : 2
      });

    } catch (error) {
      console.error('加载教练失败:', error);
      app.showToast('加载教练失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载更多
  loadMore() {
    this.loadCoaches(true);
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch() {
    this.setData({
      page: 1,
      coaches: [],
      hasMore: true
    });
    this.loadCoaches();
  },

  // 筛选变化
  onFilterChange(e) {
    const { value } = e.currentTarget.dataset;
    
    if (value === this.data.selectedFilter) return;

    this.setData({
      selectedFilter: value,
      page: 1,
      coaches: [],
      hasMore: true
    });
    this.loadCoaches();
  },

  // 预约教练
  bookCoach(e) {
    e.stopPropagation();
    
    const { id } = e.currentTarget.dataset;
    
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再预约教练',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 跳转到预约页面
    wx.navigateTo({
      url: `/pages/booking/booking?coachId=${id}`
    });
  },

  // 查看教练详情
  goToCoachDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/coach-detail/coach-detail?id=${id}`
    });
  }
});
