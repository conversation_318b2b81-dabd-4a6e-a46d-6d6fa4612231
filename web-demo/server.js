const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 8080;

// 启用 CORS
app.use(cors());

// 提供静态文件服务
app.use(express.static(__dirname));

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🌐 Web 演示服务器已启动`);
    console.log(`📖 访问地址: http://localhost:${PORT}`);
    console.log(`🎯 请确保后端 API 服务器也在运行 (端口 3000)`);
    console.log(`🚀 启动后端: cd backend && NODE_ENV=test node test-server.js`);
});

module.exports = app;
