// pages/packages/packages.js
const app = getApp();

Page({
  data: {
    packages: [],
    loading: false,
    hasMore: true,
    page: 1,
    limit: 10,
    selectedCategory: 'all',
    categories: [
      { label: '全部', value: 'all', icon: '📚' },
      { label: '基础入门', value: '基础入门', icon: '🎱' },
      { label: '技巧提升', value: '技巧提升', icon: '🎯' },
      { label: '专业训练', value: '专业训练', icon: '🏆' },
      { label: '比赛指导', value: '比赛指导', icon: '🥇' },
      { label: '私人定制', value: '私人定制', icon: '⭐' }
    ]
  },

  onLoad() {
    console.log('课程包页面加载');
    this.loadPackages();
  },

  onShow() {
    // 设置底部导航栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      });
    }
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  // 刷新数据
  onRefresh() {
    this.setData({
      page: 1,
      packages: [],
      hasMore: true
    });
    this.loadPackages().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载课程包数据
  async loadPackages(isLoadMore = false) {
    if (this.data.loading) return;
    
    this.setData({ loading: true });

    try {
      const { page, limit, selectedCategory } = this.data;
      
      const params = {
        page: isLoadMore ? page : 1,
        limit
      };

      // 添加分类筛选
      if (selectedCategory !== 'all') {
        params.category = selectedCategory;
      }

      const res = await app.request({
        url: '/packages',
        method: 'GET',
        data: params
      });

      const newPackages = res.data.packages || [];
      const packages = isLoadMore ? 
        [...this.data.packages, ...newPackages] : 
        newPackages;

      this.setData({
        packages,
        hasMore: newPackages.length === limit,
        page: isLoadMore ? page + 1 : 2
      });

    } catch (error) {
      console.error('加载课程包失败:', error);
      app.showToast('加载课程包失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载更多
  loadMore() {
    this.loadPackages(true);
  },

  // 分类变化
  onCategoryChange(e) {
    const { value } = e.currentTarget.dataset;
    
    if (value === this.data.selectedCategory) return;

    this.setData({
      selectedCategory: value,
      page: 1,
      packages: [],
      hasMore: true
    });
    this.loadPackages();
  },

  // 购买课程包
  buyPackage(e) {
    e.stopPropagation();
    
    const { id } = e.currentTarget.dataset;
    
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再购买课程包',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 显示购买确认
    const packageItem = this.data.packages.find(pkg => pkg.id === id);
    if (packageItem) {
      wx.showModal({
        title: '确认购买',
        content: `确定要购买"${packageItem.name}"吗？\n价格：¥${packageItem.price}`,
        confirmText: '确认购买',
        success: (res) => {
          if (res.confirm) {
            this.confirmBuyPackage(id);
          }
        }
      });
    }
  },

  // 确认购买课程包
  async confirmBuyPackage(packageId) {
    app.showLoading('创建订单中...');

    try {
      // 创建订单
      const orderRes = await app.request({
        url: '/orders',
        method: 'POST',
        data: {
          packageId,
          paymentMethod: 'wechat'
        }
      });

      app.hideLoading();
      
      wx.showModal({
        title: '订单创建成功',
        content: `订单号：${orderRes.data.orderNumber}\n请在30分钟内完成支付`,
        confirmText: '立即支付',
        cancelText: '稍后支付',
        success: (res) => {
          if (res.confirm) {
            // 跳转到支付页面
            wx.navigateTo({
              url: `/pages/payment/payment?orderId=${orderRes.data.id}`
            });
          } else {
            // 跳转到订单页面
            wx.switchTab({
              url: '/pages/orders/orders'
            });
          }
        }
      });

    } catch (error) {
      app.hideLoading();
      console.error('购买失败:', error);
      app.showToast(error.message || '购买失败');
    }
  },

  // 查看课程包详情
  goToPackageDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/package-detail/package-detail?id=${id}`
    });
  }
});
