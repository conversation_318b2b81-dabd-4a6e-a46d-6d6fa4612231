/* pages/profile/profile.wxss */

.login-section {
  text-align: center;
  padding: 120rpx 40rpx;
}

.login-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
}

.avatar-icon {
  font-size: 60rpx;
  color: #999;
}

.login-text {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.login-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
}

.profile-section {
  padding: 40rpx;
}

.user-info {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #5a9178, #4a7c59);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.avatar-text {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.user-phone {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.user-type {
  background: #5a9178;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  display: inline-block;
}

.menu-section {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 32rpx;
  margin-right: 24rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.menu-arrow {
  color: #ccc;
  font-size: 24rpx;
}

.logout-btn {
  width: 100%;
}
