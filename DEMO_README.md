# 🎱 Shuan-Q 台球教练预约平台 - 演示指南

## 🚀 快速开始

### 方式一：一键启动演示 (推荐)

```bash
./start-demo.sh
```

然后选择 `1` 进入交互式演示模式。

### 方式二：手动启动

1. **启动后端服务器**
```bash
cd backend
NODE_ENV=test node test-server.js
```

2. **运行演示程序** (新终端)
```bash
node demo.js
```

## 📋 演示功能

### 🎮 交互式演示菜单
- **用户登录演示** - 手机号验证码登录流程
- **教练列表查看** - 浏览可用教练信息
- **课程包浏览** - 查看课程包列表和详情
- **购买课程包** - 完整的购买和支付流程
- **我的订单** - 查看订单历史和状态
- **API 接口测试** - 查看所有可用 API
- **移动端演示** - 模拟移动端购买流程

### 🧪 功能测试
```bash
# 后端 API 测试
cd backend && NODE_ENV=test node test-packages.js

# 移动端功能测试
cd mobile-app && NODE_ENV=test node test-packages-app.js
```

### 🌐 API 文档查看
访问 http://localhost:3000 查看完整的 API 接口文档

## 📱 演示亮点

### 1. 完整的用户体验流程
- 用户注册登录 → 浏览课程包 → 购买支付 → 查看订单

### 2. 真实的业务场景
- 教练信息展示
- 课程包分类和定价
- 订单状态管理
- 支付流程模拟

### 3. 技术架构展示
- RESTful API 设计
- JWT 身份认证
- 数据库模型关联
- 错误处理机制

## 🎯 演示重点

### 后端技术栈
- **Node.js + Express** - 高性能 Web 服务器
- **Sequelize ORM** - 数据库操作和模型管理
- **JWT 认证** - 无状态身份验证
- **参数验证** - 完整的输入验证机制

### 前端技术栈
- **React Native** - 跨平台移动应用
- **Redux Toolkit** - 状态管理
- **TypeScript** - 类型安全
- **组件化设计** - 可复用的 UI 组件

### 业务功能
- **用户管理** - 注册、登录、资料管理
- **教练系统** - 教练信息、评价、搜索
- **课程包** - 创建、管理、购买
- **订单支付** - 订单创建、支付、状态跟踪
- **预约系统** - 预约创建、时间管理、状态流转

## 📊 数据展示

### 测试数据
- 2 位专业教练
- 2 个精品课程包
- 完整的用户购买记录
- 真实的业务数据流转

### API 接口
- 15+ RESTful API 接口
- 完整的 CRUD 操作
- 统一的响应格式
- 详细的错误处理

## 🎉 演示效果

### 控制台输出
- 彩色日志输出
- 详细的操作步骤
- 实时状态反馈
- 错误信息提示

### 功能验证
- ✅ 用户认证流程
- ✅ 教练信息查询
- ✅ 课程包浏览购买
- ✅ 订单创建支付
- ✅ 移动端状态管理

## 🔧 故障排除

### 常见问题

1. **端口被占用**
```bash
lsof -ti:3000 | xargs kill -9
```

2. **依赖缺失**
```bash
npm install
cd backend && npm install
cd ../mobile-app && npm install
```

3. **权限问题**
```bash
chmod +x start-demo.sh
```

### 系统要求
- Node.js >= 16.0.0
- npm 或 yarn
- macOS/Linux/Windows

## 📞 技术支持

如果在演示过程中遇到问题，请检查：
1. Node.js 版本是否符合要求
2. 所有依赖是否正确安装
3. 端口 3000 是否被占用
4. 网络连接是否正常

## 🎯 演示建议

### 推荐演示顺序
1. 先运行 **交互式演示** 了解整体功能
2. 查看 **API 文档** 了解接口设计
3. 运行 **功能测试** 验证技术实现
4. 查看 **源代码** 了解架构设计

### 重点展示内容
- 完整的业务闭环
- 优秀的用户体验
- 清晰的代码架构
- 可扩展的系统设计

---

🎉 **准备好了吗？让我们开始展示 Shuan-Q 台球教练预约平台的精彩功能！**
