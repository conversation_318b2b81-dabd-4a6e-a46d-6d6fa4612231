<!--pages/index/index.wxml-->
<view class="page-container">
  <!-- 顶部横幅 -->
  <view class="hero-section">
    <view class="hero-content">
      <text class="hero-title">🎱 专业台球教练预约</text>
      <text class="hero-subtitle">连接优秀教练与学员</text>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-number">{{stats.coaches}}+</text>
        <text class="stats-label">专业教练</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{stats.packages}}+</text>
        <text class="stats-label">精品课程</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{stats.rating}}</text>
        <text class="stats-label">平均评分</text>
      </view>
    </view>
  </view>

  <!-- 快速入口 -->
  <view class="quick-actions">
    <view class="card">
      <view class="card-header">
        <text class="card-title">快速入口</text>
      </view>
      <view class="actions-grid">
        <view class="action-item" bindtap="goToCoaches">
          <view class="action-icon">👨‍🏫</view>
          <text class="action-text">找教练</text>
        </view>
        <view class="action-item" bindtap="goToPackages">
          <view class="action-icon">📚</view>
          <text class="action-text">买课程包</text>
        </view>
        <view class="action-item" bindtap="goToOrders">
          <view class="action-icon">📋</view>
          <text class="action-text">我的订单</text>
        </view>
        <view class="action-item" bindtap="goToProfile">
          <view class="action-icon">👤</view>
          <text class="action-text">个人中心</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 热门课程包 -->
  <view class="hot-packages">
    <view class="card">
      <view class="card-header">
        <text class="card-title">🔥 热门课程包</text>
        <text class="more-link" bindtap="goToPackages">查看全部</text>
      </view>
      
      <view wx:if="{{loading}}" class="loading">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>
      
      <view wx:elif="{{packages.length === 0}}" class="empty-state">
        <text class="empty-text">暂无课程包</text>
      </view>
      
      <scroll-view wx:else scroll-x="true" class="packages-scroll">
        <view class="packages-list">
          <view 
            wx:for="{{packages}}" 
            wx:key="id" 
            class="package-item"
            bindtap="goToPackageDetail"
            data-id="{{item.id}}"
          >
            <view class="package-header">
              <text class="package-name">{{item.name}}</text>
              <view class="badge badge-info">{{item.level === 'beginner' ? '初级' : item.level === 'intermediate' ? '中级' : '高级'}}</view>
            </view>
            <text class="package-desc">{{item.description}}</text>
            <view class="package-info">
              <text class="package-sessions">{{item.totalSessions}}课时</text>
              <text class="package-validity">{{item.validityDays}}天有效</text>
            </view>
            <view class="price-container">
              <text wx:if="{{item.originalPrice > item.price}}" class="price-original">¥{{item.originalPrice}}</text>
              <text class="price-current">¥{{item.price}}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 优秀教练 -->
  <view class="top-coaches">
    <view class="card">
      <view class="card-header">
        <text class="card-title">⭐ 优秀教练</text>
        <text class="more-link" bindtap="goToCoaches">查看全部</text>
      </view>
      
      <view wx:if="{{coachLoading}}" class="loading">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>
      
      <view wx:elif="{{coaches.length === 0}}" class="empty-state">
        <text class="empty-text">暂无教练信息</text>
      </view>
      
      <view wx:else class="coaches-list">
        <view 
          wx:for="{{coaches}}" 
          wx:key="id" 
          class="coach-item"
          bindtap="goToCoachDetail"
          data-id="{{item.id}}"
        >
          <view class="coach-avatar">
            <text class="avatar-text">{{item.name.charAt(0)}}</text>
          </view>
          <view class="coach-info">
            <text class="coach-name">{{item.name}}</text>
            <view class="coach-rating">
              <text class="rating-text">⭐ {{item.rating}}</text>
              <text class="rating-count">({{item.ratingCount}}人评价)</text>
            </view>
            <text class="coach-location">📍 {{item.location}}</text>
          </view>
          <view class="coach-price">
            <text class="price-text">¥{{item.hourlyRate}}/小时</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
