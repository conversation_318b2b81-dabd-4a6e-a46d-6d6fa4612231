<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shuan-Q 移动端UI展示</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        /* 移动端容器 */
        .mobile-container {
            max-width: 375px;
            margin: 20px auto;
            background: #fff;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            position: relative;
        }
        
        /* 状态栏 */
        .status-bar {
            background: #000;
            color: #fff;
            padding: 8px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
        }
        
        /* 导航栏 */
        .navbar {
            background: #5a9178;
            color: #fff;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .navbar h1 {
            font-size: 18px;
            font-weight: 600;
        }
        
        .navbar .menu-btn {
            background: none;
            border: none;
            color: #fff;
            font-size: 18px;
            cursor: pointer;
        }
        
        /* 主要内容区域 */
        .main-content {
            height: 600px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        /* 标签页 */
        .tab-bar {
            display: flex;
            background: #fff;
            border-bottom: 1px solid #e9ecef;
        }
        
        .tab-item {
            flex: 1;
            padding: 15px 10px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }
        
        .tab-item.active {
            color: #5a9178;
            border-bottom-color: #5a9178;
        }
        
        .tab-item i {
            display: block;
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .tab-item span {
            font-size: 12px;
        }
        
        /* 内容页面 */
        .tab-content {
            display: none;
            padding: 20px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* 卡片样式 */
        .card {
            background: #fff;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .badge {
            background: #5a9178;
            color: #fff;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .badge.discount {
            background: #e74c3c;
        }
        
        .badge.level {
            background: #007bff;
        }
        
        /* 价格样式 */
        .price-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 8px 0;
        }
        
        .price-original {
            text-decoration: line-through;
            color: #999;
            font-size: 14px;
        }
        
        .price-current {
            color: #e74c3c;
            font-size: 20px;
            font-weight: bold;
        }
        
        /* 按钮样式 */
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }
        
        .btn-primary {
            background: #5a9178;
            color: #fff;
        }
        
        .btn-primary:hover {
            background: #4a7c59;
        }
        
        .btn-outline {
            background: transparent;
            color: #5a9178;
            border: 1px solid #5a9178;
        }
        
        /* 教练卡片 */
        .coach-card {
            display: flex;
            gap: 12px;
        }
        
        .coach-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #5a9178, #4a7c59);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 24px;
            flex-shrink: 0;
        }
        
        .coach-info {
            flex: 1;
        }
        
        .coach-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .coach-rating {
            color: #ffc107;
            font-size: 14px;
            margin-bottom: 4px;
        }
        
        .coach-location {
            color: #666;
            font-size: 12px;
            margin-bottom: 8px;
        }
        
        .coach-price {
            color: #e74c3c;
            font-weight: 600;
        }
        
        /* 课程包特色 */
        .features {
            margin: 12px 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 14px;
            color: #666;
        }
        
        .feature-item i {
            color: #5a9178;
            margin-right: 8px;
            width: 16px;
        }
        
        /* 订单状态 */
        .order-status {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-paid {
            background: #d1edff;
            color: #0c5460;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        /* 底部导航 */
        .bottom-nav {
            background: #fff;
            border-top: 1px solid #e9ecef;
            display: flex;
            padding: 8px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            background: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .nav-item.active {
            color: #5a9178;
        }
        
        .nav-item i {
            display: block;
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-item span {
            font-size: 10px;
        }
        
        /* 登录表单 */
        .login-form {
            padding: 40px 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #5a9178;
        }
        
        .input-group {
            display: flex;
            gap: 8px;
        }
        
        .input-group .form-input {
            flex: 1;
        }
        
        .input-group .btn {
            width: auto;
            padding: 12px 16px;
        }
        
        /* 响应式 */
        @media (max-width: 480px) {
            .mobile-container {
                margin: 0;
                border-radius: 0;
                max-width: 100%;
                height: 100vh;
            }
        }
        
        /* 动画 */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #5a9178;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>9:41</span>
            <span>Shuan-Q</span>
            <span>100% <i class="fas fa-battery-full"></i></span>
        </div>
        
        <!-- 导航栏 -->
        <div class="navbar">
            <button class="menu-btn">
                <i class="fas fa-bars"></i>
            </button>
            <h1>🎱 Shuan-Q</h1>
            <button class="menu-btn" onclick="showLogin()">
                <i class="fas fa-user"></i>
            </button>
        </div>
        
        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 标签页导航 -->
            <div class="tab-bar">
                <button class="tab-item active" onclick="showTab('home')">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </button>
                <button class="tab-item" onclick="showTab('coaches')">
                    <i class="fas fa-users"></i>
                    <span>教练</span>
                </button>
                <button class="tab-item" onclick="showTab('packages')">
                    <i class="fas fa-graduation-cap"></i>
                    <span>课程包</span>
                </button>
                <button class="tab-item" onclick="showTab('orders')">
                    <i class="fas fa-shopping-cart"></i>
                    <span>订单</span>
                </button>
                <button class="tab-item" onclick="showTab('profile')">
                    <i class="fas fa-user-circle"></i>
                    <span>我的</span>
                </button>
            </div>
            
            <!-- 首页内容 -->
            <div id="home" class="tab-content active">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">🎉 欢迎使用 Shuan-Q</h2>
                    </div>
                    <p style="color: #666; margin-bottom: 16px;">专业的台球教练预约平台</p>
                    <div style="display: flex; justify-content: space-around; text-align: center;">
                        <div>
                            <div style="font-size: 24px; font-weight: bold; color: #5a9178;">2+</div>
                            <div style="font-size: 12px; color: #666;">专业教练</div>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: bold; color: #5a9178;">2+</div>
                            <div style="font-size: 12px; color: #666;">精品课程</div>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: bold; color: #5a9178;">4.8</div>
                            <div style="font-size: 12px; color: #666;">平均评分</div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🔥 热门课程包</h3>
                        <span style="color: #5a9178; font-size: 14px;">查看全部 ></span>
                    </div>
                    <div style="display: flex; gap: 12px; overflow-x: auto;">
                        <div style="min-width: 200px; background: #f8f9fa; padding: 12px; border-radius: 8px;">
                            <h4 style="font-size: 14px; margin-bottom: 8px;">台球基础入门</h4>
                            <div class="price-container">
                                <span class="price-original">¥2000</span>
                                <span class="price-current">¥1500</span>
                            </div>
                            <div style="font-size: 12px; color: #666;">10课时 • 60天有效</div>
                        </div>
                        <div style="min-width: 200px; background: #f8f9fa; padding: 12px; border-radius: 8px;">
                            <h4 style="font-size: 14px; margin-bottom: 8px;">技巧提升训练</h4>
                            <div class="price-container">
                                <span class="price-original">¥2500</span>
                                <span class="price-current">¥2000</span>
                            </div>
                            <div style="font-size: 12px; color: #666;">8课时 • 45天有效</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 教练列表 -->
            <div id="coaches" class="tab-content">
                <div class="card">
                    <div class="coach-card">
                        <div class="coach-avatar">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="coach-info">
                            <div class="coach-name">张教练</div>
                            <div class="coach-rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                4.9 (120人评价)
                            </div>
                            <div class="coach-location">
                                <i class="fas fa-map-marker-alt"></i>
                                北京市朝阳区
                            </div>
                            <div class="coach-price">¥200/小时</div>
                        </div>
                    </div>
                    <div style="margin-top: 12px;">
                        <div style="font-size: 14px; color: #666; margin-bottom: 8px;">
                            专长: 斯诺克, 九球
                        </div>
                        <button class="btn btn-primary">立即预约</button>
                    </div>
                </div>
                
                <div class="card">
                    <div class="coach-card">
                        <div class="coach-avatar">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="coach-info">
                            <div class="coach-name">李教练</div>
                            <div class="coach-rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                                4.7 (85人评价)
                            </div>
                            <div class="coach-location">
                                <i class="fas fa-map-marker-alt"></i>
                                上海市浦东新区
                            </div>
                            <div class="coach-price">¥180/小时</div>
                        </div>
                    </div>
                    <div style="margin-top: 12px;">
                        <div style="font-size: 14px; color: #666; margin-bottom: 8px;">
                            专长: 中式八球, 斯诺克
                        </div>
                        <button class="btn btn-primary">立即预约</button>
                    </div>
                </div>
            </div>
            
            <!-- 课程包列表 -->
            <div id="packages" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">台球基础入门课程包</h3>
                        <span class="badge level">初级</span>
                    </div>
                    <p style="color: #666; font-size: 14px; margin-bottom: 12px;">
                        适合零基础学员的台球入门课程，包含基本姿势、瞄准技巧等
                    </p>
                    <div class="features">
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            一对一专业指导
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            基础姿势纠正
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            瞄准技巧训练
                        </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <div>
                            <span style="font-size: 12px; color: #666;">10课时 • 60天有效</span>
                        </div>
                        <span class="badge discount">省25%</span>
                    </div>
                    <div class="price-container">
                        <span class="price-original">¥2000</span>
                        <span class="price-current">¥1500</span>
                    </div>
                    <button class="btn btn-primary" onclick="purchasePackage('pkg-001')">立即购买</button>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">台球技巧提升课程包</h3>
                        <span class="badge level">中级</span>
                    </div>
                    <p style="color: #666; font-size: 14px; margin-bottom: 12px;">
                        适合有一定基础的学员，提升台球技巧和战术水平
                    </p>
                    <div class="features">
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            高级技巧训练
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            战术分析
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            比赛模拟
                        </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <div>
                            <span style="font-size: 12px; color: #666;">8课时 • 45天有效</span>
                        </div>
                        <span class="badge discount">省20%</span>
                    </div>
                    <div class="price-container">
                        <span class="price-original">¥2500</span>
                        <span class="price-current">¥2000</span>
                    </div>
                    <button class="btn btn-primary" onclick="purchasePackage('pkg-002')">立即购买</button>
                </div>
            </div>
            
            <!-- 订单列表 -->
            <div id="orders" class="tab-content">
                <div id="orders-content">
                    <div style="text-align: center; padding: 40px 20px; color: #666;">
                        <i class="fas fa-shopping-cart" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                        <p>请先登录查看订单</p>
                        <button class="btn btn-primary" style="margin-top: 16px;" onclick="showLogin()">
                            立即登录
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 个人中心 -->
            <div id="profile" class="tab-content">
                <div id="profile-content">
                    <div class="login-form">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <i class="fas fa-user-circle" style="font-size: 80px; color: #ddd;"></i>
                            <h2 style="margin-top: 16px;">登录 Shuan-Q</h2>
                            <p style="color: #666;">体验专业的台球教学服务</p>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">手机号</label>
                            <input type="tel" class="form-input" id="mobile-phone" value="13800138000" readonly>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">验证码</label>
                            <div class="input-group">
                                <input type="text" class="form-input" id="mobile-code" placeholder="请输入验证码">
                                <button class="btn btn-outline" onclick="sendMobileCode()">获取验证码</button>
                            </div>
                            <small style="color: #666; font-size: 12px;">测试环境验证码: 123456</small>
                        </div>
                        
                        <button class="btn btn-primary" onclick="mobileLogin()">
                            <span id="login-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                            登录
                        </button>
                        
                        <div id="mobile-result" style="margin-top: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="mobile-app.js"></script>
</body>
</html>
