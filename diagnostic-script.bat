@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🔍 开始 Shuan-Q 项目环境诊断...
echo ==================================

echo.
echo 1. 检查基础环境...
echo -------------------

:: 检查 Node.js
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Node.js 已安装
    for /f "tokens=*" %%i in ('node --version') do echo 📋 Node.js 版本: %%i
) else (
    echo ❌ Node.js 未安装
    echo 💡 请从 https://nodejs.org/ 下载安装
)

:: 检查 npm
npm --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ npm 已安装
    for /f "tokens=*" %%i in ('npm --version') do echo 📋 npm 版本: %%i
) else (
    echo ❌ npm 未安装
)

:: 检查 npx
npx --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ npx 已安装
) else (
    echo ❌ npx 未安装
    echo 💡 尝试: npm install -g npx
)

echo.
echo 2. 检查网络和镜像源...
echo ----------------------

:: 检查 npm 镜像源
for /f "tokens=*" %%i in ('npm config get registry 2^>nul') do (
    echo 📋 当前 npm 镜像源: %%i
    echo %%i | findstr "npmjs.org" >nul
    if !errorlevel! == 0 (
        echo 💡 建议使用国内镜像源: npm config set registry https://registry.npmmirror.com/
    )
)

echo.
echo 3. 检查端口占用...
echo ------------------

:: 检查端口 3000
netstat -an | findstr ":3000" >nul 2>&1
if %errorlevel% == 0 (
    echo ⚠️  端口 3000 被占用
    netstat -ano | findstr ":3000"
) else (
    echo ✅ 端口 3000 可用
)

:: 检查端口 8080
netstat -an | findstr ":8080" >nul 2>&1
if %errorlevel% == 0 (
    echo ⚠️  端口 8080 被占用
    netstat -ano | findstr ":8080"
) else (
    echo ✅ 端口 8080 可用
)

echo.
echo 4. 检查项目依赖...
echo ------------------

:: 检查 package.json
if exist "package.json" (
    echo ✅ package.json 存在
    
    :: 检查 node_modules
    if exist "node_modules" (
        echo ✅ node_modules 存在
    ) else (
        echo ⚠️  node_modules 不存在，需要运行: npm install
    )
) else (
    echo ❌ package.json 不存在
)

echo.
echo 5. 系统信息...
echo -------------

echo 📋 操作系统: %OS%
echo 📋 处理器架构: %PROCESSOR_ARCHITECTURE%
echo 📋 当前用户: %USERNAME%
echo 📋 当前目录: %CD%

echo.
echo 6. 快速修复建议...
echo ------------------

echo 🔧 常见问题快速修复命令:
echo.
echo 清理 npm 缓存:
echo   npm cache clean --force
echo.
echo 重新安装依赖:
echo   rmdir /s /q node_modules
echo   del package-lock.json
echo   npm install
echo.
echo 杀死占用端口的进程 (以3000为例):
echo   for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do taskkill /pid %%a /f
echo.
echo 设置国内镜像源:
echo   npm config set registry https://registry.npmmirror.com/
echo.
echo 安装常用全局包:
echo   npm install -g nodemon
echo   npm install -g pm2

echo.
echo ==================================
echo 🎉 诊断完成！
echo.
echo 💡 如果问题仍然存在，请检查:
echo 1. Windows防火墙设置
echo 2. 杀毒软件拦截
echo 3. 系统环境变量 PATH
echo 4. 管理员权限
echo.
echo 📚 详细解决方案请查看: development-issues-analysis.md

pause
