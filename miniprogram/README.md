# 🎱 Shuan-Q 小程序版本

## 📱 小程序开发工具导入指南

### 1. 准备工作
- 下载并安装 [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- 确保后端API服务器正在运行 (http://localhost:3000)

### 2. 导入项目
1. 打开微信开发者工具
2. 选择 "导入项目"
3. 项目目录选择: `miniprogram` 文件夹
4. AppID: 选择 "测试号" 或输入 `wxtest123456`
5. 项目名称: `Shuan-Q台球教练预约平台`
6. 点击 "导入"

### 3. 配置网络请求域名
由于小程序的安全限制，需要配置合法域名：

**开发阶段解决方案:**
1. 在微信开发者工具中
2. 点击右上角 "详情"
3. 勾选 "不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

**生产环境:**
- 需要在微信公众平台配置 `https://your-domain.com` 为合法域名

### 4. 启动后端服务器
```bash
cd backend
NODE_ENV=test node test-server.js
```

### 5. 小程序功能特色

#### 🏠 首页
- 统计数据展示
- 快速入口导航
- 热门课程包
- 优秀教练推荐

#### 👨‍🏫 教练页面
- 教练列表展示
- 筛选和搜索
- 教练详情查看

#### 📚 课程包页面
- 课程包列表
- 分类筛选
- 价格展示
- 购买功能

#### 🛒 订单页面
- 订单列表
- 状态管理
- 支付功能

#### 👤 个人中心
- 用户登录
- 个人信息
- 设置功能

### 6. 技术特点

#### 🎨 UI设计
- 原生小程序组件
- 响应式布局
- 品牌色彩统一
- 流畅的交互动画

#### 🔧 功能实现
- 网络请求封装
- 状态管理
- 本地存储
- 错误处理

#### 📱 用户体验
- 下拉刷新
- 上拉加载
- 加载状态
- 空状态处理

### 7. 开发调试

#### 控制台日志
- 查看 Console 面板的日志输出
- 网络请求状态
- 错误信息提示

#### 网络面板
- 查看 Network 面板
- 监控API请求
- 检查响应数据

#### 模拟器测试
- 不同设备尺寸测试
- 网络状态模拟
- 性能分析

### 8. 常见问题

#### Q: 小程序无法加载？
A: 检查以下几点：
1. 项目目录是否正确 (选择 miniprogram 文件夹)
2. app.json 文件是否存在
3. 是否勾选了 "不校验合法域名"

#### Q: 网络请求失败？
A: 检查以下几点：
1. 后端服务器是否启动 (http://localhost:3000)
2. 是否勾选了 "不校验合法域名"
3. 网络连接是否正常

#### Q: 页面显示异常？
A: 检查以下几点：
1. wxml 文件语法是否正确
2. wxss 样式是否加载
3. 数据绑定是否正确

### 9. 项目结构
```
miniprogram/
├── app.js              # 小程序入口文件
├── app.json            # 小程序配置文件
├── app.wxss            # 全局样式文件
├── sitemap.json        # 站点地图配置
├── project.config.json # 项目配置文件
└── pages/              # 页面目录
    ├── index/          # 首页
    ├── coaches/        # 教练页面
    ├── packages/       # 课程包页面
    ├── orders/         # 订单页面
    ├── profile/        # 个人中心
    └── login/          # 登录页面
```

### 10. 下一步开发
- [ ] 完善所有页面功能
- [ ] 添加支付功能
- [ ] 优化用户体验
- [ ] 添加更多交互动画
- [ ] 性能优化

---

🎉 **现在您可以在微信开发者工具中体验完整的小程序功能了！**
