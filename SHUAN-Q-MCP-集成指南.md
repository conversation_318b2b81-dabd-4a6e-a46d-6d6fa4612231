# Shuan-Q 项目 MCP Feedback Enhanced 集成指南

## 🎯 项目背景

**Shuan-Q** 是一个台球教练预约平台，包含以下组件：
- 📱 **移动应用** (React Native)
- 🖥️ **后端服务** (Node.js/Express)
- 📦 **共享模块** (TypeScript)
- 🌐 **Web 演示** (HTML/JavaScript)
- 📱 **微信小程序** (WeChat Mini Program)

## 🚨 历史问题回顾

根据 `development-issues-analysis.md`，项目曾遇到的主要问题：
- ❌ `npm: command not found`
- ❌ `npx: command not found`
- ❌ 包安装失败或依赖冲突
- ❌ 端口占用和服务冲突
- ❌ 权限问题和环境配置错误

## 🛡️ MCP Feedback Enhanced 如何解决这些问题

### 1. 环境命令保护
**问题**: AI 可能执行有害的 npm/npx 命令
**解决**: AI 在执行任何命令前都会通过界面征求确认

```bash
# 之前: AI 直接执行
npm install some-package

# 现在: AI 会先询问
"我准备执行 'npm install some-package'，这样做可以吗？"
```

### 2. 依赖管理安全
**问题**: 意外安装错误版本或冲突的包
**解决**: 在修改 package.json 或安装依赖前获得确认

### 3. 端口冲突预防
**问题**: 多个服务争用同一端口
**解决**: AI 会询问端口使用情况并建议解决方案

### 4. 代码修改审查
**问题**: AI 可能做出不当的代码更改
**解决**: 展示修改计划，获得确认后再执行

## 🔧 集成步骤

### 步骤 1: 选择配置模式

#### 本地开发 (推荐桌面应用)
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DESKTOP_MODE": "true",
        "MCP_WEB_PORT": "8765",
        "MCP_DEBUG": "false"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

#### 远程开发 (Web UI)
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_PORT": "8765"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### 步骤 2: 配置 Cursor/AI 助手

1. 打开 Cursor 设置
2. 找到 MCP 配置部分
3. 添加上述配置
4. 重启 Cursor

### 步骤 3: 设置项目特定规则

在 AI 助手中添加以下项目特定规则：

```
# Shuan-Q 项目 MCP 规则

1. 在执行任何 npm/yarn 命令前，必须调用 mcp-feedback-enhanced 征求确认
2. 在修改 package.json、tsconfig.json 等配置文件前，必须展示更改内容并获得确认
3. 在启动服务器或更改端口配置前，必须检查端口可用性并询问
4. 在安装新依赖或更新现有依赖前，必须说明原因并获得确认
5. 在修改数据库配置或连接字符串前，必须获得明确授权
6. 在执行可能影响多个工作区的操作前，必须列出影响范围并确认
```

## 🎯 项目特定使用场景

### 场景 1: 安装新依赖
```
AI: "我需要为移动应用安装 react-native-vector-icons。
     这将修改 mobile-app/package.json 并可能需要原生代码链接。
     是否继续？"

用户: 通过界面确认或拒绝
```

### 场景 2: 启动开发服务器
```
AI: "我准备启动以下服务：
     - 后端服务器 (端口 3000)
     - 移动应用开发服务器 (端口 8081)
     - Web 演示服务器 (端口 8080)
     
     是否继续启动？"
```

### 场景 3: 数据库操作
```
AI: "我需要修改数据库连接配置：
     - 更新 backend/src/config/database.js
     - 修改连接字符串指向新的测试数据库
     
     这样做安全吗？"
```

### 场景 4: 构建和部署
```
AI: "准备执行构建流程：
     1. npm run build:backend
     2. npm run build:mobile
     3. 生成生产环境配置
     
     确认执行构建？"
```

## 🔍 监控和调试

### 查看 MCP 连接状态
- 桌面应用: 查看应用窗口状态指示器
- Web UI: 访问 http://localhost:8765 查看连接状态

### 调试模式
如需调试，临时启用：
```json
"env": {
  "MCP_DEBUG": "true"
}
```

### 日志查看
- MCP 日志: 在 AI 助手的 MCP 日志中查看
- 应用日志: 在反馈界面的控制台中查看

## 🚀 最佳实践

### 1. 开发工作流
1. 启动 MCP Feedback Enhanced
2. 在 AI 助手中描述需要完成的任务
3. 通过反馈界面确认每个步骤
4. 监控执行结果并提供反馈

### 2. 团队协作
- 共享配置文件确保团队一致性
- 建立标准的确认流程
- 记录重要决策和更改

### 3. 安全考虑
- 始终在非生产环境中测试
- 定期备份重要配置文件
- 谨慎处理敏感信息（API密钥、数据库密码等）

## 🎉 预期效果

集成 MCP Feedback Enhanced 后，你将获得：

✅ **更安全的开发环境** - 避免意外的破坏性操作
✅ **更好的代码质量** - AI 修改前的审查机制
✅ **更高的开发效率** - 减少错误和重复工作
✅ **更强的控制能力** - 对 AI 操作的完全掌控
✅ **更少的环境问题** - 避免重复之前的 npm/npx 问题

## 📞 获取帮助

如果在集成过程中遇到问题：

1. 查看 `MCP-FEEDBACK-ENHANCED-安装指南.md`
2. 检查 `development-issues-analysis.md` 中的常见问题
3. 访问 GitHub 项目页面获取最新信息
4. 通过反馈界面报告具体问题

---

**记住**: MCP Feedback Enhanced 是你的安全网，让 AI 辅助开发变得更加可控和安全！
