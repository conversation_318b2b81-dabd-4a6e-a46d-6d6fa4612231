// pages/index/index.js
const app = getApp();

Page({
  data: {
    stats: {
      coaches: 2,
      packages: 2,
      rating: 4.8
    },
    packages: [],
    coaches: [],
    loading: false,
    coachLoading: false
  },

  onLoad() {
    console.log('首页加载');
    this.loadData();
  },

  onShow() {
    // 页面显示时刷新数据
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }
  },

  onPullDownRefresh() {
    this.loadData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载页面数据
  async loadData() {
    await Promise.all([
      this.loadPackages(),
      this.loadCoaches()
    ]);
  },

  // 加载课程包数据
  async loadPackages() {
    this.setData({ loading: true });
    
    try {
      const res = await app.request({
        url: '/packages',
        method: 'GET',
        data: {
          page: 1,
          limit: 3
        }
      });
      
      this.setData({
        packages: res.data.packages || []
      });
    } catch (error) {
      console.error('加载课程包失败:', error);
      app.showToast('加载课程包失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载教练数据
  async loadCoaches() {
    this.setData({ coachLoading: true });
    
    try {
      const res = await app.request({
        url: '/coaches',
        method: 'GET',
        data: {
          page: 1,
          limit: 3
        }
      });
      
      this.setData({
        coaches: res.data.coaches || []
      });
    } catch (error) {
      console.error('加载教练失败:', error);
      app.showToast('加载教练失败');
    } finally {
      this.setData({ coachLoading: false });
    }
  },

  // 导航到教练页面
  goToCoaches() {
    wx.switchTab({
      url: '/pages/coaches/coaches'
    });
  },

  // 导航到课程包页面
  goToPackages() {
    wx.switchTab({
      url: '/pages/packages/packages'
    });
  },

  // 导航到订单页面
  goToOrders() {
    wx.switchTab({
      url: '/pages/orders/orders'
    });
  },

  // 导航到个人中心
  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    });
  },

  // 导航到课程包详情
  goToPackageDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/package-detail/package-detail?id=${id}`
    });
  },

  // 导航到教练详情
  goToCoachDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/coach-detail/coach-detail?id=${id}`
    });
  }
});
