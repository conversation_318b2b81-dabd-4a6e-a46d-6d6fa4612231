<!--pages/coaches/coaches.wxml-->
<view class="page-container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <input 
        class="search-input" 
        placeholder="搜索教练姓名或专长" 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <view class="search-icon" bindtap="onSearch">🔍</view>
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <scroll-view scroll-x="true" class="filter-scroll">
      <view class="filter-list">
        <view 
          wx:for="{{filters}}" 
          wx:key="value"
          class="filter-item {{selectedFilter === item.value ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-value="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 教练列表 -->
  <view class="coaches-container">
    <view wx:if="{{loading}}" class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>

    <view wx:elif="{{coaches.length === 0}}" class="empty-state">
      <view class="empty-icon">👨‍🏫</view>
      <text class="empty-text">暂无教练信息</text>
      <button class="btn btn-primary" bindtap="onRefresh">刷新</button>
    </view>

    <view wx:else class="coaches-list">
      <view 
        wx:for="{{coaches}}" 
        wx:key="id"
        class="coach-card"
        bindtap="goToCoachDetail"
        data-id="{{item.id}}"
      >
        <!-- 教练头像 -->
        <view class="coach-avatar">
          <text class="avatar-text">{{item.name.charAt(0)}}</text>
        </view>

        <!-- 教练信息 -->
        <view class="coach-info">
          <view class="coach-header">
            <text class="coach-name">{{item.name}}</text>
            <view class="coach-rating">
              <text class="rating-stars">⭐⭐⭐⭐⭐</text>
              <text class="rating-score">{{item.rating}}</text>
            </view>
          </view>

          <text class="coach-desc">{{item.description}}</text>

          <view class="coach-details">
            <view class="detail-item">
              <text class="detail-icon">📍</text>
              <text class="detail-text">{{item.location}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-icon">🎯</text>
              <text class="detail-text">{{item.specialties.join('、')}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-icon">👥</text>
              <text class="detail-text">{{item.ratingCount}}人评价</text>
            </view>
          </view>

          <view class="coach-footer">
            <view class="coach-price">
              <text class="price-label">时薪</text>
              <text class="price-value">¥{{item.hourlyRate}}</text>
            </view>
            <button class="btn btn-primary coach-book-btn" bindtap="bookCoach" data-id="{{item.id}}">
              立即预约
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{hasMore && !loading}}" class="load-more">
    <button class="btn btn-outline" bindtap="loadMore">加载更多</button>
  </view>
</view>
