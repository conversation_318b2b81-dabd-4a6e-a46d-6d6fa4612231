// pages/package-detail/package-detail.js
const app = getApp();

Page({
  data: {
    packageInfo: {},
    loading: false
  },

  onLoad(options) {
    const { id } = options;
    if (id) {
      this.loadPackageDetail(id);
    }
  },

  async loadPackageDetail(id) {
    this.setData({ loading: true });
    
    try {
      const res = await app.request({
        url: `/packages/${id}`,
        method: 'GET'
      });
      
      this.setData({
        packageInfo: res.data
      });
    } catch (error) {
      console.error('加载课程包详情失败:', error);
      app.showToast('加载失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  buyPackage() {
    // 购买逻辑
    app.showToast('购买功能开发中');
  }
});
