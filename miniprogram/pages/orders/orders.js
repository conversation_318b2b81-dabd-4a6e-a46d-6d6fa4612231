// pages/orders/orders.js
const app = getApp();

Page({
  data: {
    orders: [],
    loading: false,
    isLogin: false
  },

  onLoad() {
    console.log('订单页面加载');
  },

  onShow() {
    this.setData({
      isLogin: app.globalData.isLogin
    });
    
    if (app.globalData.isLogin) {
      this.loadOrders();
    }

    // 设置底部导航栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      });
    }
  },

  async loadOrders() {
    this.setData({ loading: true });
    
    try {
      const res = await app.request({
        url: '/orders/my',
        method: 'GET'
      });
      
      this.setData({
        orders: res.data.orders || []
      });
    } catch (error) {
      console.error('加载订单失败:', error);
      app.showToast('加载订单失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  goToPackages() {
    wx.switchTab({
      url: '/pages/packages/packages'
    });
  },

  async payOrder(e) {
    const { id } = e.currentTarget.dataset;
    
    try {
      await app.request({
        url: `/orders/${id}/pay`,
        method: 'POST'
      });
      
      app.showToast('支付成功', 'success');
      this.loadOrders();
    } catch (error) {
      app.showToast('支付失败');
    }
  }
});
