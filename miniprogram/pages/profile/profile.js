// pages/profile/profile.js
const app = getApp();

Page({
  data: {
    isLogin: false,
    userInfo: null
  },

  onShow() {
    this.setData({
      isLogin: app.globalData.isLogin,
      userInfo: app.globalData.userInfo
    });

    // 设置底部导航栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4
      });
    }
  },

  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  goToOrders() {
    wx.switchTab({
      url: '/pages/orders/orders'
    });
  },

  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.logout();
          this.setData({
            isLogin: false,
            userInfo: null
          });
          app.showToast('已退出登录');
        }
      }
    });
  }
});
