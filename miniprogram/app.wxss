/* app.wxss */
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 全局样式变量 */
:root {
  --primary-color: #5a9178;
  --secondary-color: #f8f9fa;
  --accent-color: #e74c3c;
  --text-color: #333;
  --border-color: #e9ecef;
}

/* 容器样式 */
.container {
  padding: 20rpx;
}

.page-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 按钮样式 */
.btn {
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: #5a9178;
  color: #fff;
}

.btn-primary:active {
  background: #4a7c59;
}

.btn-outline {
  background: transparent;
  color: #5a9178;
  border: 2rpx solid #5a9178;
}

.btn-outline:active {
  background: #f0f8f5;
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  background: #ccc !important;
  color: #999 !important;
}

/* 徽章样式 */
.badge {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.badge-primary {
  background: #5a9178;
  color: #fff;
}

.badge-danger {
  background: #e74c3c;
  color: #fff;
}

.badge-warning {
  background: #ffc107;
  color: #333;
}

.badge-info {
  background: #007bff;
  color: #fff;
}

/* 价格样式 */
.price-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.price-original {
  text-decoration: line-through;
  color: #999;
  font-size: 28rpx;
}

.price-current {
  color: #e74c3c;
  font-size: 40rpx;
  font-weight: bold;
}

/* 列表样式 */
.list-item {
  background: #fff;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 28rpx;
  color: #666;
}

.list-item-arrow {
  color: #ccc;
  font-size: 24rpx;
}

/* 表单样式 */
.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 16rpx;
  font-size: 32rpx;
  background: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #5a9178;
}

.input-group {
  display: flex;
  gap: 16rpx;
}

.input-group .form-input {
  flex: 1;
}

/* 状态样式 */
.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-paid {
  background: #d1edff;
  color: #0c5460;
}

.status-completed {
  background: #d4edda;
  color: #155724;
}

/* 加载样式 */
.loading {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #666;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #5a9178;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 32rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  color: #666;
}

.empty-icon {
  font-size: 120rpx;
  color: #ddd;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.mb-16 {
  margin-bottom: 16rpx;
}

.mb-24 {
  margin-bottom: 24rpx;
}

.mb-32 {
  margin-bottom: 32rpx;
}

.mt-16 {
  margin-top: 16rpx;
}

.mt-24 {
  margin-top: 24rpx;
}

.mt-32 {
  margin-top: 32rpx;
}

.p-16 {
  padding: 16rpx;
}

.p-24 {
  padding: 24rpx;
}

.p-32 {
  padding: 32rpx;
}
