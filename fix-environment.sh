#!/bin/bash

# 🔧 Shuan-Q 项目环境自动修复脚本
# 自动修复常见的开发环境问题

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 Shuan-Q 项目环境自动修复工具${NC}"
echo "=================================="

# 检查是否为 root 用户
if [ "$EUID" -eq 0 ]; then
    echo -e "${YELLOW}⚠️  检测到 root 用户，某些操作可能不需要 sudo${NC}"
fi

# 1. 修复 Node.js 和 npm
echo -e "${BLUE}1. 检查和修复 Node.js 环境...${NC}"

if ! command -v node &> /dev/null; then
    echo -e "${YELLOW}📦 安装 Node.js...${NC}"
    
    # 检测操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install node
        else
            echo -e "${RED}❌ 请先安装 Homebrew: https://brew.sh/${NC}"
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v apt-get &> /dev/null; then
            # Ubuntu/Debian
            curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
            sudo apt-get install -y nodejs
        elif command -v yum &> /dev/null; then
            # CentOS/RHEL
            curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
            sudo yum install -y nodejs
        else
            echo -e "${RED}❌ 不支持的 Linux 发行版${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ 不支持的操作系统: $OSTYPE${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Node.js 已安装${NC}"
fi

# 2. 修复 npm 权限
echo -e "${BLUE}2. 修复 npm 权限...${NC}"

# 创建全局目录
mkdir -p ~/.npm-global

# 配置 npm 使用新目录
npm config set prefix '~/.npm-global'

# 添加到 PATH
if ! grep -q "~/.npm-global/bin" ~/.bashrc; then
    echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
    echo -e "${GREEN}✅ 已添加 npm 全局路径到 ~/.bashrc${NC}"
fi

if ! grep -q "~/.npm-global/bin" ~/.zshrc 2>/dev/null; then
    echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.zshrc 2>/dev/null || true
    echo -e "${GREEN}✅ 已添加 npm 全局路径到 ~/.zshrc${NC}"
fi

# 3. 配置 npm 镜像源
echo -e "${BLUE}3. 配置 npm 镜像源...${NC}"

current_registry=$(npm config get registry)
if [[ "$current_registry" == *"npmjs.org"* ]]; then
    npm config set registry https://registry.npmmirror.com/
    echo -e "${GREEN}✅ 已设置国内镜像源${NC}"
else
    echo -e "${GREEN}✅ 镜像源已配置${NC}"
fi

# 4. 清理和重新安装依赖
echo -e "${BLUE}4. 清理和重新安装项目依赖...${NC}"

if [ -f "package.json" ]; then
    echo -e "${YELLOW}🧹 清理旧的依赖...${NC}"
    rm -rf node_modules package-lock.json
    
    echo -e "${YELLOW}📦 重新安装依赖...${NC}"
    npm cache clean --force
    npm install
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
else
    echo -e "${YELLOW}⚠️  未找到 package.json，跳过依赖安装${NC}"
fi

# 5. 修复端口占用
echo -e "${BLUE}5. 检查和修复端口占用...${NC}"

ports=(3000 8080 3001)
for port in "${ports[@]}"; do
    if lsof -i :$port &> /dev/null; then
        echo -e "${YELLOW}⚠️  端口 $port 被占用，尝试释放...${NC}"
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        echo -e "${GREEN}✅ 端口 $port 已释放${NC}"
    else
        echo -e "${GREEN}✅ 端口 $port 可用${NC}"
    fi
done

# 6. 安装常用全局包
echo -e "${BLUE}6. 安装常用全局包...${NC}"

global_packages=("nodemon" "pm2" "http-server")
for package in "${global_packages[@]}"; do
    if ! npm list -g $package &> /dev/null; then
        echo -e "${YELLOW}📦 安装 $package...${NC}"
        npm install -g $package
    else
        echo -e "${GREEN}✅ $package 已安装${NC}"
    fi
done

# 7. 创建启动脚本
echo -e "${BLUE}7. 创建项目启动脚本...${NC}"

cat > start-project.sh << 'EOF'
#!/bin/bash

# Shuan-Q 项目启动脚本

echo "🚀 启动 Shuan-Q 项目..."

# 检查后端服务器
if [ -f "backend/test-server.js" ]; then
    echo "📡 启动后端服务器..."
    cd backend
    NODE_ENV=test node test-server.js &
    BACKEND_PID=$!
    cd ..
    echo "后端服务器 PID: $BACKEND_PID"
else
    echo "❌ 未找到后端服务器文件"
fi

# 检查前端服务器
if [ -f "web-demo/index.html" ]; then
    echo "🌐 启动前端服务器..."
    cd web-demo
    python3 -m http.server 8080 &
    FRONTEND_PID=$!
    cd ..
    echo "前端服务器 PID: $FRONTEND_PID"
else
    echo "❌ 未找到前端文件"
fi

echo ""
echo "🎉 项目启动完成！"
echo "📖 前端地址: http://localhost:8080"
echo "📡 后端地址: http://localhost:3000"
echo "📱 移动端: http://localhost:8080/mobile.html"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待用户中断
trap 'echo "正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT
wait
EOF

chmod +x start-project.sh
echo -e "${GREEN}✅ 已创建启动脚本 start-project.sh${NC}"

# 8. 创建环境检查脚本
echo -e "${BLUE}8. 创建环境检查脚本...${NC}"

cat > check-environment.sh << 'EOF'
#!/bin/bash

echo "🔍 检查 Shuan-Q 项目环境..."

# 检查 Node.js
if command -v node &> /dev/null; then
    echo "✅ Node.js: $(node --version)"
else
    echo "❌ Node.js 未安装"
fi

# 检查 npm
if command -v npm &> /dev/null; then
    echo "✅ npm: $(npm --version)"
else
    echo "❌ npm 未安装"
fi

# 检查项目依赖
if [ -f "package.json" ] && [ -d "node_modules" ]; then
    echo "✅ 项目依赖已安装"
else
    echo "❌ 项目依赖未安装"
fi

# 检查端口
ports=(3000 8080)
for port in "${ports[@]}"; do
    if lsof -i :$port &> /dev/null; then
        echo "⚠️  端口 $port 被占用"
    else
        echo "✅ 端口 $port 可用"
    fi
done

echo "环境检查完成！"
EOF

chmod +x check-environment.sh
echo -e "${GREEN}✅ 已创建环境检查脚本 check-environment.sh${NC}"

echo ""
echo "=================================="
echo -e "${GREEN}🎉 环境修复完成！${NC}"
echo ""
echo -e "${BLUE}📋 修复内容总结:${NC}"
echo "1. ✅ Node.js 和 npm 环境"
echo "2. ✅ npm 权限和镜像源"
echo "3. ✅ 项目依赖"
echo "4. ✅ 端口占用"
echo "5. ✅ 全局工具包"
echo "6. ✅ 启动脚本"
echo ""
echo -e "${BLUE}🚀 下一步操作:${NC}"
echo "1. 重新加载 shell 配置: source ~/.bashrc"
echo "2. 启动项目: ./start-project.sh"
echo "3. 检查环境: ./check-environment.sh"
echo ""
echo -e "${YELLOW}💡 如果仍有问题，请运行诊断脚本: ./diagnostic-script.sh${NC}"
