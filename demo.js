#!/usr/bin/env node

const axios = require('axios');
const readline = require('readline');

const BASE_URL = 'http://localhost:3000';

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

class ShuanQDemo {
  constructor() {
    this.token = null;
    this.currentUser = null;
  }

  async showWelcome() {
    console.clear();
    colorLog('cyan', '🎱 ===============================================');
    colorLog('cyan', '🎉    欢迎体验 Shuan-Q 台球教练预约平台    🎉');
    colorLog('cyan', '===============================================');
    console.log();
    colorLog('yellow', '📋 功能演示菜单:');
    console.log('1. 🔐 用户登录演示');
    console.log('2. 👨‍🏫 教练列表查看');
    console.log('3. 📚 课程包浏览');
    console.log('4. 💳 购买课程包');
    console.log('5. 📅 预约管理');
    console.log('6. 📊 我的订单');
    console.log('7. 🌐 API 接口测试');
    console.log('8. 📱 移动端演示');
    console.log('0. 退出演示');
    console.log();
  }

  async checkServerStatus() {
    try {
      const response = await axios.get(`${BASE_URL}/health`);
      colorLog('green', '✅ 服务器运行正常');
      return true;
    } catch (error) {
      colorLog('red', '❌ 服务器未启动，请先启动服务器');
      colorLog('yellow', '启动命令: cd backend && NODE_ENV=test node test-server.js');
      return false;
    }
  }

  async demoLogin() {
    colorLog('blue', '\n🔐 === 用户登录演示 ===');
    
    try {
      // 发送验证码
      colorLog('yellow', '📱 正在发送验证码...');
      const codeResponse = await axios.post(`${BASE_URL}/api/auth/send-code`, {
        phone: '13800138000'
      });
      colorLog('green', '✅ 验证码发送成功 (测试环境固定为: 123456)');
      
      // 登录
      colorLog('yellow', '🔑 正在登录...');
      const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
        phone: '13800138000',
        code: '123456'
      });
      
      this.token = loginResponse.data.data.token;
      this.currentUser = loginResponse.data.data.user;
      
      colorLog('green', `✅ 登录成功！欢迎 ${this.currentUser.nickname}`);
      colorLog('cyan', `📱 手机号: ${this.currentUser.phone}`);
      colorLog('cyan', `👤 用户类型: ${this.currentUser.userType}`);
      
    } catch (error) {
      colorLog('red', `❌ 登录失败: ${error.response?.data?.error || error.message}`);
    }
    
    await question('\n按回车键继续...');
  }

  async demoCoaches() {
    colorLog('blue', '\n👨‍🏫 === 教练列表演示 ===');
    
    try {
      const response = await axios.get(`${BASE_URL}/api/coaches`);
      const coaches = response.data.data.coaches;
      
      colorLog('green', `✅ 找到 ${coaches.length} 位教练:`);
      
      coaches.forEach((coach, index) => {
        console.log(`\n${index + 1}. ${coach.name}`);
        colorLog('cyan', `   ⭐ 评分: ${coach.rating} (${coach.ratingCount}人评价)`);
        colorLog('cyan', `   📍 位置: ${coach.location}`);
        colorLog('cyan', `   💰 时薪: ¥${coach.hourlyRate}/小时`);
        colorLog('cyan', `   🎯 专长: ${coach.specialties.join(', ')}`);
        colorLog('cyan', `   📝 简介: ${coach.description}`);
      });
      
    } catch (error) {
      colorLog('red', `❌ 获取教练列表失败: ${error.response?.data?.error || error.message}`);
    }
    
    await question('\n按回车键继续...');
  }

  async demoPackages() {
    colorLog('blue', '\n📚 === 课程包浏览演示 ===');
    
    try {
      const response = await axios.get(`${BASE_URL}/api/packages`);
      const packages = response.data.data.packages;
      
      colorLog('green', `✅ 找到 ${packages.length} 个课程包:`);
      
      packages.forEach((pkg, index) => {
        console.log(`\n${index + 1}. ${pkg.name}`);
        colorLog('cyan', `   📝 描述: ${pkg.description}`);
        colorLog('cyan', `   💰 价格: ¥${pkg.price} ${pkg.originalPrice > pkg.price ? `(原价¥${pkg.originalPrice})` : ''}`);
        colorLog('cyan', `   📚 课时: ${pkg.totalSessions}节`);
        colorLog('cyan', `   ⏰ 有效期: ${pkg.validityDays}天`);
        colorLog('cyan', `   🏷️ 分类: ${pkg.category} | 级别: ${pkg.level}`);
        colorLog('cyan', `   ✨ 特色: ${pkg.features.slice(0, 2).join(', ')}`);
      });
      
    } catch (error) {
      colorLog('red', `❌ 获取课程包失败: ${error.response?.data?.error || error.message}`);
    }
    
    await question('\n按回车键继续...');
  }

  async demoPurchase() {
    colorLog('blue', '\n💳 === 购买课程包演示 ===');
    
    if (!this.token) {
      colorLog('yellow', '⚠️ 请先登录');
      return;
    }
    
    try {
      // 创建订单
      colorLog('yellow', '📋 正在创建订单...');
      const orderResponse = await axios.post(`${BASE_URL}/api/orders`, {
        packageId: 'pkg-001',
        paymentMethod: 'wechat'
      }, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      const order = orderResponse.data.data;
      colorLog('green', '✅ 订单创建成功!');
      colorLog('cyan', `📋 订单号: ${order.orderNumber}`);
      colorLog('cyan', `💰 金额: ¥${order.amount}`);
      colorLog('cyan', `📦 课程包: ${order.package.name}`);
      
      // 模拟支付
      colorLog('yellow', '\n💳 正在支付...');
      const payResponse = await axios.post(`${BASE_URL}/api/orders/${order.id}/pay`, {}, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      const paidOrder = payResponse.data.data;
      colorLog('green', '🎉 支付成功!');
      colorLog('cyan', `💳 支付ID: ${paidOrder.paymentId}`);
      colorLog('cyan', `✅ 状态: ${paidOrder.status}`);
      
    } catch (error) {
      colorLog('red', `❌ 购买失败: ${error.response?.data?.error || error.message}`);
    }
    
    await question('\n按回车键继续...');
  }

  async demoOrders() {
    colorLog('blue', '\n📊 === 我的订单演示 ===');
    
    if (!this.token) {
      colorLog('yellow', '⚠️ 请先登录');
      return;
    }
    
    try {
      const response = await axios.get(`${BASE_URL}/api/orders/my`, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      const orders = response.data.data.orders;
      colorLog('green', `✅ 共有 ${orders.length} 个订单:`);
      
      orders.forEach((order, index) => {
        console.log(`\n${index + 1}. 订单号: ${order.orderNumber}`);
        colorLog('cyan', `   📦 课程包: ${order.package?.name || '未知'}`);
        colorLog('cyan', `   💰 金额: ¥${order.amount}`);
        colorLog('cyan', `   📊 状态: ${order.status}`);
        colorLog('cyan', `   📅 创建时间: ${new Date(order.createdAt).toLocaleString()}`);
        if (order.paidAt) {
          colorLog('cyan', `   💳 支付时间: ${new Date(order.paidAt).toLocaleString()}`);
        }
      });
      
    } catch (error) {
      colorLog('red', `❌ 获取订单失败: ${error.response?.data?.error || error.message}`);
    }
    
    await question('\n按回车键继续...');
  }

  async demoAPI() {
    colorLog('blue', '\n🌐 === API 接口测试 ===');
    
    try {
      const response = await axios.get(`${BASE_URL}/`);
      const apiInfo = response.data;
      
      colorLog('green', '✅ API 服务器信息:');
      colorLog('cyan', `📖 服务名称: ${apiInfo.message}`);
      colorLog('cyan', `🔢 版本: ${apiInfo.version}`);
      colorLog('cyan', `📊 状态: ${apiInfo.status}`);
      
      console.log('\n📋 可用接口:');
      Object.entries(apiInfo.endpoints).forEach(([category, endpoints]) => {
        colorLog('yellow', `\n${category.toUpperCase()}:`);
        endpoints.forEach(endpoint => {
          console.log(`   ${endpoint}`);
        });
      });
      
    } catch (error) {
      colorLog('red', `❌ API 测试失败: ${error.message}`);
    }
    
    await question('\n按回车键继续...');
  }

  async demoMobile() {
    colorLog('blue', '\n📱 === 移动端演示 ===');
    colorLog('yellow', '正在运行移动端购买流程演示...\n');
    
    try {
      // 运行移动端测试
      const { spawn } = require('child_process');
      const mobileDemo = spawn('node', ['mobile-app/test-packages-app.js'], {
        env: { ...process.env, NODE_ENV: 'test' }
      });
      
      mobileDemo.stdout.on('data', (data) => {
        process.stdout.write(data);
      });
      
      mobileDemo.stderr.on('data', (data) => {
        process.stderr.write(data);
      });
      
      await new Promise((resolve) => {
        mobileDemo.on('close', resolve);
      });
      
    } catch (error) {
      colorLog('red', `❌ 移动端演示失败: ${error.message}`);
    }
    
    await question('\n按回车键继续...');
  }

  async run() {
    // 检查服务器状态
    const serverOk = await this.checkServerStatus();
    if (!serverOk) {
      rl.close();
      return;
    }

    while (true) {
      await this.showWelcome();
      const choice = await question('请选择功能 (0-8): ');
      
      switch (choice) {
        case '1':
          await this.demoLogin();
          break;
        case '2':
          await this.demoCoaches();
          break;
        case '3':
          await this.demoPackages();
          break;
        case '4':
          await this.demoPurchase();
          break;
        case '5':
          colorLog('yellow', '\n📅 预约管理功能请参考 Phase 2 演示');
          await question('按回车键继续...');
          break;
        case '6':
          await this.demoOrders();
          break;
        case '7':
          await this.demoAPI();
          break;
        case '8':
          await this.demoMobile();
          break;
        case '0':
          colorLog('green', '\n🎉 感谢体验 Shuan-Q 台球教练预约平台！');
          rl.close();
          return;
        default:
          colorLog('red', '❌ 无效选择，请重新输入');
          await question('按回车键继续...');
      }
    }
  }
}

// 运行演示
if (require.main === module) {
  const demo = new ShuanQDemo();
  demo.run().catch(console.error);
}

module.exports = ShuanQDemo;
